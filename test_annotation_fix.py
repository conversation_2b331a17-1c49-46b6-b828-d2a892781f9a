#!/usr/bin/env python3
"""
Test script to verify that the make_serializable() fix resolves the issue
with missing fields in the Mistral document_annotation.
"""

import json
import requests

def test_annotation_completeness():
    """Test that all fields from the input annotation are preserved in the output."""
    
    # Test data based on rawdata.md structure
    test_data = [
        {
            "pages": [
                {
                    "markdown": "# Test Invoice\n\nVendor: BCN Telecom\nInvoice: 23918420"
                }
            ],
            "document_annotation": json.dumps({
                "Vendor Name": "BCN Telecom",
                "Invoice Number": "23918420", 
                "Customer Number": "BOC20326",
                "Current Charges": "$442.92",
                "Finance Charges": "$0.00",
                "Invoice Date": "5/1/2025",
                "Current Charges Due (USD)": "$442.92",
                "Services_detail": {
                    "Service Charges": "$366.90",
                    "Usage": "$0.00",
                    "Taxes and Surcharges": "$76.02",
                    "Service Category": {
                        "1.25G X 35M Cable Internet": "$330.95",
                        "5 Static IPs": "$20.00",
                        "Standard Modem Equipment": "$15.95"
                    }
                }
            })
        },
        {
            "original_extracted_text": "Test PyMuPDF text",
            "llm_friendly_markdown_output": {
                "markdown_chunks": ["# Test markdown"]
            }
        }
    ]
    
    # Send request to Flask API
    url = "http://localhost:5001/sanitize_texts"
    headers = {"Content-Type": "application/json"}
    
    print("🧪 Testing annotation completeness...")
    print("📤 Sending test data to Flask API...")
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        
        if response.status_code != 200:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
        result = response.json()
        
        # Extract the annotation from the response
        output_annotation = result.get("mistral_document_annotation")
        
        if not output_annotation:
            print("❌ No mistral_document_annotation found in response")
            return False
            
        print("✅ API request successful")
        print(f"📊 Response status: {result.get('status')}")
        
        # Check for the previously missing fields
        services_detail = output_annotation.get("Services_detail", {})
        
        missing_fields = []
        
        # Check for "Usage" field
        if "Usage" not in services_detail:
            missing_fields.append("Services_detail.Usage")
        else:
            print(f"✅ Found Usage: {services_detail['Usage']}")
            
        # Check for "Taxes and Surcharges" field  
        if "Taxes and Surcharges" not in services_detail:
            missing_fields.append("Services_detail.Taxes and Surcharges")
        else:
            print(f"✅ Found Taxes and Surcharges: {services_detail['Taxes and Surcharges']}")
            
        # Check all top-level fields
        expected_top_level = [
            "Vendor Name", "Invoice Number", "Customer Number", 
            "Current Charges", "Finance Charges", "Invoice Date",
            "Current Charges Due (USD)", "Services_detail"
        ]
        
        for field in expected_top_level:
            if field not in output_annotation:
                missing_fields.append(field)
            else:
                print(f"✅ Found {field}: {output_annotation[field]}")
                
        # Check Service Category nested fields
        service_category = services_detail.get("Service Category", {})
        expected_services = [
            "1.25G X 35M Cable Internet", "5 Static IPs", "Standard Modem Equipment"
        ]
        
        for service in expected_services:
            if service not in service_category:
                missing_fields.append(f"Services_detail.Service Category.{service}")
            else:
                print(f"✅ Found {service}: {service_category[service]}")
        
        if missing_fields:
            print(f"\n❌ Missing fields detected:")
            for field in missing_fields:
                print(f"   - {field}")
            return False
        else:
            print(f"\n🎉 SUCCESS: All fields preserved in annotation!")
            print(f"📊 Total top-level fields: {len(output_annotation)}")
            print(f"📊 Services_detail fields: {len(services_detail)}")
            print(f"📊 Service Category fields: {len(service_category)}")
            return True
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🔬 Testing Mistral document_annotation field preservation")
    print("=" * 60)
    
    success = test_annotation_completeness()
    
    print("=" * 60)
    if success:
        print("✅ TEST PASSED: make_serializable() fix successful!")
    else:
        print("❌ TEST FAILED: Issues still exist with annotation processing")
        
    exit(0 if success else 1)
