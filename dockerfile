# Use an official Python runtime as a parent image
FROM python:3.9-slim

# Set DEBIAN_FRONTEND to noninteractive to avoid prompts during installation
ENV DEBIAN_FRONTEND=noninteractive

# Set the working directory in the container
WORKDIR /app

# Install system dependencies:
# build-essential for C compilation (if needed by some pip packages)
# tesseract-ocr for OCR capabilities
# tesseract-ocr-eng for English language data for Tesseract
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    tesseract-ocr \
    tesseract-ocr-eng \
    && rm -rf /var/lib/apt/lists/*

# Set the TESSDATA_PREFIX environment variable
# For Debian-based systems where tesseract-ocr installs version 4.x.x,
# the data is often in /usr/share/tesseract-ocr/4.00/tessdata
# TESSDATA_PREFIX should point to the parent of the 'tessdata' directory.
ENV TESSDATA_PREFIX /usr/share/tesseract-ocr/4.00

# Copy the dependencies file to the working directory
COPY requirements.txt .

# Install any needed packages specified in requirements.txt
# Ensure Flask, PyMuPDF, pymupdf4llm are in your requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy the current directory contents into the container at /app
# This includes your app.py (or whatever your main Flask script is named)
COPY . .

# Make port 5000 available to the world outside this container
EXPOSE 5000

# Define environment variable (you had this, keeping it)
ENV NAME="PyMuPDF_API_OCR" 

# Run app.py when the container launches (assuming your Flask app file is app.py)
CMD ["python", "app.py"]