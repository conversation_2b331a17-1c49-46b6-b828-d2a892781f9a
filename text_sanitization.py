import re
import logging

# Configure logging for this module
logger = logging.getLogger(__name__)

# --- Library Imports & Availability Checks ---
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    from markdown_it import MarkdownIt
    MARKDOWN_IT_AVAILABLE = True
except ImportError:
    MARKDOWN_IT_AVAILABLE = False

def normalize_text(text):
    """Normalize text for consistent comparison."""
    if not text:
        return ""
    text = text.replace("'", "'").replace("'", "'").replace(""", '"').replace(""", '"')
    text = text.replace('\u00A0', ' ')
    text = re.sub(r'\s+', ' ', text).strip()
    return " ".join(text.lower().strip().split())

def get_text_from_html_using_bs(html_content):
    """Extract plain text from HTML content using BeautifulSoup."""
    if not html_content:
        return ""
    if not BS4_AVAILABLE:
        logger.warning("BeautifulSoup not available. Applying basic regex strip.")
        text = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.DOTALL | re.IGNORECASE)
        text = re.sub(r'<[^>]+>', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
        
    try:
        soup = BeautifulSoup(html_content, "lxml")
    except Exception as e_lxml:
        logger.warning(f"BeautifulSoup failed with lxml: {e_lxml}. Trying html.parser.")
        try:
            soup = BeautifulSoup(html_content, "html.parser")
        except Exception as e_parser:
            logger.error(f"BeautifulSoup parsing error with html.parser: {e_parser}. Returning raw content.")
            return html_content
            
    for script_or_style in soup(["script", "style"]):
        script_or_style.decompose()

    for table in soup.find_all("table"):
        table_text_rows = []
        for row in table.find_all("tr"):
            cells = [cell.get_text(separator=" ", strip=True) for cell in row.find_all(["th", "td"])]
            if any(c.strip() for c in cells):
                table_text_rows.append("\t".join(cells))
        table_content_str = "\n" + "\n".join(table_text_rows) + "\n"
        table.replace_with(soup.new_string(table_content_str))
        
    plain_text = soup.get_text(separator="\n", strip=True)
    return plain_text

def convert_markdown_to_plain_text_with_library(md_text):
    """Convert markdown to plain text."""
    if not md_text:
        return ""
    html_from_markdown = md_text
    if MARKDOWN_IT_AVAILABLE:
        md_parser = MarkdownIt(options_update={"html": True})
        try:
            html_from_markdown = md_parser.render(md_text)
        except Exception as e:
            logger.warning(f"MarkdownIt render error: {e}. Will attempt to process input as direct HTML/text.")
    else:
        logger.warning("markdown-it-py not available. Input will be processed as direct HTML/text.")
    
    plain_text = get_text_from_html_using_bs(html_from_markdown)
    
    if plain_text:
        text = plain_text
        text = text.replace("\\$", "$")
        text = re.sub(r'\$\s*(.*?)\s*\$', r'\1', text)
        text = re.sub(r'\\quad', ' ', text); text = re.sub(r'\\square', '[ ]', text)
        text = re.sub(r'^\s*>\s?', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*[\*\-\+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'\n\s*\n+', '\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        plain_text = text.strip()
        
        # Apply OCR-specific corrections
        plain_text = correct_ocr_errors(plain_text)
        plain_text = clean_ocr_artifacts(plain_text)
        plain_text = reconstruct_broken_words(plain_text)
        
    return plain_text

def correct_ocr_errors(text):
    """Correct common OCR errors in text."""
    if not text:
        return ""
    
    ocr_corrections = [
        (r'\bl\b', 'I'),  # Standalone 'l' to 'I'
        (r'\bO\b', '0'),  # Standalone 'O' to '0' in numeric contexts
        (r'(\d)\s*l\s*(\d)', r'\g<1>1\g<2>'),  # 'l' between digits to '1'
        (r'(\d)\s*O\s*(\d)', r'\g<1>0\g<2>'),  # 'O' between digits to '0'
        (r'(\d)\s*S\s*(\d)', r'\g<1>5\g<2>'),  # 'S' between digits to '5'
        (r'(\d)\s*I\s*(\d)', r'\g<1>1\g<2>'),  # 'I' between digits to '1'
        
        # Currency corrections
        (r'(\$)\s*([Oo])', r'\g<1>0'),  # '$O' to '$0'
        (r'(\$)\s*([Il])', r'\g<1>1'),  # '$I' or '$l' to '$1'
        
        # Common word corrections
        (r'\bInvoice\b', 'Invoice'),
        (r'\bAccount\b', 'Account'),
        (r'\bDate\b', 'Date'),
        (r'\bTotal\b', 'Total'),
        (r'\bAmount\b', 'Amount'),
        (r'\bDue\b', 'Due'),
        
        # Space around punctuation
        (r'(\w)\s*,\s*(\w)', r'\g<1>, \g<2>'),
        (r'(\w)\s*\.\s*(\w)', r'\g<1>. \g<2>'),
        (r'(\w)\s*:\s*(\w)', r'\g<1>: \g<2>'),
    ]
    
    corrected_text = text
    for pattern, replacement in ocr_corrections:
        try:
            corrected_text = re.sub(pattern, replacement, corrected_text)
        except re.error as e:
            logger.warning(f"OCR correction regex error for pattern {pattern}: {e}")
    
    return corrected_text

def clean_ocr_artifacts(text):
    """Remove common OCR artifacts from text."""
    if not text:
        return ""
    
    artifact_patterns = [
        r'[^\w\s\$\.\,\:\;\!\?\-\(\)\[\]\{\}\"\'\/\\\@\#\%\&\*\+\=\<\>\|\~\`\^\n\t]',  # Non-standard chars
        r'(?<!\w)[\.\,\:\;\!\?]{2,}(?!\w)',  # Multiple punctuation
        r'\s+[\.\,\:\;]\s+',  # Spaced punctuation
        r'(?<=[a-zA-Z])[\_\|]{1,3}(?=[a-zA-Z])',  # Underscores/pipes in words
        r'(?<=\d)[\_\|]{1,3}(?=\d)',  # Underscores/pipes in numbers
        r'(?<!\w)[\_\|]{1,3}(?!\w)',  # Standalone underscores/pipes
        r'\s*\|\s*',  # Pipe characters with spaces
    ]
    
    cleaned_text = text
    for pattern in artifact_patterns:
        try:
            cleaned_text = re.sub(pattern, ' ', cleaned_text)
        except re.error as e:
            logger.warning(f"Artifact removal regex error for pattern {pattern}: {e}")
    
    # Clean up excessive whitespace
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = re.sub(r'\n\s*\n+', '\n\n', cleaned_text)
    
    return cleaned_text.strip()

def reconstruct_broken_words(text):
    """Reconstruct common broken words from OCR."""
    if not text:
        return ""
    
    word_reconstruction_patterns = [
        (r'\bIn\s*voice\b', 'Invoice'),
        (r'\bAc\s*count\b', 'Account'),
        (r'\bTo\s*tal\b', 'Total'),
        (r'\bA\s*mount\b', 'Amount'),
        (r'\bPay\s*ment\b', 'Payment'),
        (r'\bBal\s*ance\b', 'Balance'),
        (r'\bSer\s*vice\b', 'Service'),
        (r'\bCus\s*tomer\b', 'Customer'),
        (r'\bTe\s*le\s*com\b', 'Telecom'),
        (r'\bTe\s*le\s*phone\b', 'Telephone'),
        
        # Date patterns
        (r'(\d{1,2})\s*/\s*(\d{1,2})\s*/\s*(\d{2,4})', r'\g<1>/\g<2>/\g<3>'),
        
        # Currency patterns
        (r'\$\s*(\d)', r'$\g<1>'),
        (r'(\d)\s*\.\s*(\d{2})', r'\g<1>.\g<2>'),
    ]
    
    reconstructed_text = text
    for pattern, replacement in word_reconstruction_patterns:
        try:
            reconstructed_text = re.sub(pattern, replacement, reconstructed_text, flags=re.IGNORECASE)
        except re.error as e:
            logger.warning(f"Word reconstruction regex error for pattern {pattern}: {e}")
    
    return reconstructed_text

def fix_concatenated_words(text):
    """Fix words that have been concatenated without spaces."""
    if not text:
        return ""
    
    concatenation_patterns = [
        # Common invoice field concatenations
        (r'accountnumber', 'account number'),
        (r'invoicenumber', 'invoice number'),
        (r'invoicedate', 'invoice date'),
        (r'duedate', 'due date'),
        (r'totalamount', 'total amount'),
        (r'amountdue', 'amount due'),
        (r'servicecharges', 'service charges'),
        (r'customermessages', 'customer messages'),
        (r'paymentmethod', 'payment method'),
        (r'billingaddress', 'billing address'),
        
        # Surcharge concatenations
        (r'surchargeaccount', 'surcharge account'),
        (r'surchargenetwork', 'surcharge network'),
        (r'surchargeproperty', 'surcharge property'),
        (r'surchargeregulatory', 'surcharge regulatory'),
        
        # Amount concatenations
        (r'amountstate', 'amount state'),
        (r'amountrecurring', 'amount recurring'),
        
        # Report concatenations
        (r'reportdate', 'report date'),
        (r'reportservice', 'report service'),
        (r'reportdescription', 'report description'),
        
        # Location concatenations
        (r'locationproductid', 'location productid'),
        
        # Generic word boundary fixes
        (r'([a-z])([A-Z])', r'\1 \2'),  # camelCase to spaced
    ]
    
    fixed_text = text
    for pattern, replacement in concatenation_patterns:
        try:
            fixed_text = re.sub(pattern, replacement, fixed_text, flags=re.IGNORECASE)
        except re.error as e:
            logger.warning(f"Concatenation fix regex error for pattern {pattern}: {e}")
    
    return fixed_text

def normalize_table_formatting(text):
    """Normalize table formatting across different invoice types."""
    if not text:
        return ""
    
    normalized_text = text
    
    table_normalization_patterns = [
        # Normalize table separators
        (r'---+', '---'),  # Standardize dashes
        (r'===+', '==='),  # Standardize equals
        
        # Normalize currency formatting in tables
        (r'\$\s+(\d)', r'$\1'),  # Remove space after dollar sign
        (r'(\d)\s+(\.\d{2})', r'\1\2'),  # Remove space before decimal
        
        # Clean up table totals formatting
        (r'(?i)total\s*:\s*\$?\s*(\d)', r'total: $\1'),
        (r'(?i)subtotal\s*:\s*\$?\s*(\d)', r'subtotal: $\1'),
        
        # Normalize percentage formatting
        (r'(\d)\s+%', r'\1%'),  # Remove space before percent
        
        # Clean up table column alignment artifacts
        (r'\s{2,}([A-Z]{2,})', r' \1'),  # Multiple spaces before uppercase words
        (r'([a-z])\s{2,}([A-Z])', r'\1 \2'),  # Multiple spaces between case changes
    ]
    
    for pattern, replacement in table_normalization_patterns:
        try:
            normalized_text = re.sub(pattern, replacement, normalized_text, flags=re.MULTILINE)
        except re.error as e:
            logger.warning(f"Table normalization regex error for pattern {pattern}: {e}")
    
    # Remove standalone table artifacts
    lines = normalized_text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line_stripped = line.strip()
        
        # Skip lines that are likely table artifacts
        if re.match(r'^[-\s]{3,}$', line_stripped):  # Only dashes and spaces
            continue
        if re.match(r'^[|+\s]{3,}$', line_stripped):  # Table border characters
            continue
        if re.match(r'^[.]{3,}$', line_stripped):  # Dotted lines
            continue
        if re.match(r'^[=]{3,}$', line_stripped):  # Equal sign lines
            continue
        if line_stripped in ['---', '--- ---', '--- --- ---']:  # Common table separators
            continue
            
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def clean_generic_artifacts(text):
    """Remove common artifacts that appear across different invoice types."""
    if not text:
        return ""
    
    generic_artifact_patterns = [
        # Page numbering artifacts
        r'(?i)page\s+(?:number\s*:?\s*)?\d+(?:\s+of\s+\d+)?(?:\s*$|\n)',
        
        # Image/file references
        r'!\[[\w\-\.]+\]\([\w\-\.]+\)',  # Markdown image references
        r'(?i)img[-_]\d+\.(?:jpeg|jpg|png|gif)',  # Image filenames
        
        # Barcode/encoding artifacts
        r'\b\d{15,}\b',  # Long numeric strings (likely barcodes)
        r'[A-Z0-9]{20,}',  # Long alphanumeric strings
        
        # OCR confidence/processing artifacts
        r'(?i)(?:ocr|confidence|processing)\s*:?\s*\d+%?',
        
        # Generic separator artifacts
        r'^[^\w\s]*$',  # Lines with only symbols/punctuation
        
        # Empty field indicators
        r'\$_+',  # Dollar sign with underscores (blank amount fields)
        r'\$\s*_+',  # Dollar sign with spaced underscores
        
        # Repeated punctuation artifacts
        r'\.{4,}',  # More than 3 dots in a row
        r',{3,}',   # Multiple commas
        r':{3,}',   # Multiple colons
    ]
    
    cleaned_text = text
    for pattern in generic_artifact_patterns:
        try:
            cleaned_text = re.sub(pattern, ' ', cleaned_text, flags=re.MULTILINE)
        except re.error as e:
            logger.warning(f"Generic artifact removal regex error for pattern {pattern}: {e}")
    
    # Clean up excessive whitespace
    cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)
    cleaned_text = re.sub(r'[ \t]+', ' ', cleaned_text)
    
    return cleaned_text.strip()

def intelligent_boilerplate_removal(text, similarity_threshold=0.8):
    """Remove boilerplate text using pattern matching."""
    if not text:
        return ""
    
    boilerplate_patterns = [
        r'(?i)thank\s+you\s+for\s+(?:your\s+)?(?:business|choosing|using)[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)if\s+you\s+have\s+(?:any\s+)?questions[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)please\s+(?:do\s+not\s+hesitate\s+to\s+)?contact\s+us[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)we\s+appreciate\s+your[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)visit\s+(?:us\s+)?(?:at\s+)?www\.[\w.]+[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)(?:call|phone)\s+us\s+at\s+[\d\-\(\)\s.]+[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Marketing content patterns
        r'(?i)(?:transforming|converting)[\s\S]*?(?:cost[_\s]effective|future[_\s]proof|cloud[_\s]enabled)[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)(?:reliability|performance|technology)[\s\S]*?(?:superior|advanced|innovative|cutting[_\s]edge)[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)voice\s+data\s+cloud\s+wireless[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)array\s+of\s+ip\s+voice\s+solutions[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)let\s+us\s+match\s+the\s+right\s+technology[\s\S]*?(?=\n\s*\n|\Z)',
    ]
    
    cleaned_text = text
    for pattern in boilerplate_patterns:
        try:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.MULTILINE | re.DOTALL)
        except re.error as e:
            logger.warning(f"Intelligent boilerplate removal regex error for pattern {pattern}: {e}")
    
    return cleaned_text

def structure_aware_cleanup(text):
    """Apply structure-aware cleanup to preserve important invoice data."""
    if not text:
        return ""
    
    lines = text.splitlines()
    cleaned_lines = []
    
    for line in lines:
        line_stripped = line.strip()
        
        # Skip empty lines and obvious artifacts
        if not line_stripped:
            continue
        if re.match(r'^[-_=\*]{5,}$', line_stripped):
            continue
        if re.match(r'^[.]{5,}$', line_stripped):
            continue
        if len(line_stripped) == 1 and line_stripped in '|-+*=':
            continue
            
        # Preserve lines with invoice data indicators
        invoice_indicators = [
            'invoice', 'account', 'total', 'amount', 'due', 'date', 'payment',
            'balance', 'service', 'charge', 'tax', 'customer', '$'
        ]
        
        if any(indicator in line_stripped.lower() for indicator in invoice_indicators):
            cleaned_lines.append(line)
        elif len(line_stripped) > 10:  # Keep substantial content
            cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def apply_block_removal_rules(text, rules_list):
    """Apply block removal rules to text."""
    if not text or not rules_list:
        return text
    
    processed_text = intelligent_boilerplate_removal(text)
    
    for rule in rules_list:
        rule_id = rule.get("id", "N/A")
        try:
            rule_type = rule.get("type", "starts_with")
            
            if rule_type == "between_exact_markers":
                start_marker = rule.get("start_marker")
                end_marker = rule.get("end_marker")
                if start_marker and end_marker:
                    start_regex = re.escape(start_marker)
                    end_regex = re.escape(end_marker)
                    pattern = r'(?s)' + start_regex + r'.*?' + end_regex
                    processed_text = re.sub(pattern, '', processed_text, flags=re.IGNORECASE)
                    
            elif rule_type == "starts_with":
                starts_with_phrase = rule.get("starts_with")
                if not starts_with_phrase:
                    continue
                start_phrase_regex = r"\s*" + re.escape(starts_with_phrase)
                
                if rule.get("ends_before_next_blank_line"):
                    pattern = start_phrase_regex + r"[\s\S]*?(?=\n\s*\n|\Z)"
                elif rule.get("ends_before_phrase"):
                    ends_before_phrase_regex = re.escape(rule.get("ends_before_phrase"))
                    pattern = start_phrase_regex + r"[\s\S]*?(?=" + ends_before_phrase_regex + r")"
                elif rule.get("ends_with_phrase"):
                    ends_with_phrase_regex = re.escape(rule.get("ends_with_phrase"))
                    pattern = start_phrase_regex + r"[\s\S]*?" + ends_with_phrase_regex
                else:
                    pattern = start_phrase_regex + r"[\s\S]*?(?=\n\s*\n|\Z)"
                
                if pattern:
                    processed_text = re.sub(pattern, '', processed_text, flags=re.DOTALL | re.IGNORECASE)
                    
            elif rule_type == "regex_direct":
                regex_pattern = rule.get("regex_pattern")
                if regex_pattern:
                    processed_text = re.sub(regex_pattern, '', processed_text, flags=re.DOTALL | re.IGNORECASE)
                    
        except re.error as e:
            logger.warning(f"Regex error applying rule ID '{rule_id}': {e}")
        except Exception as ex:
            logger.warning(f"General error applying rule ID '{rule_id}': {ex}")
    
    return processed_text

def minimal_pymupdf_cleanup(text):
    """
    Enhanced minimal cleanup for PyMuPDF text - focuses on fixing OCR artifacts
    while preserving all legitimate document structure and content.
    """
    if not text:
        return ""
    
    import re
    
    lines = text.split('\n')
    cleaned_lines = []
    
    for i, line in enumerate(lines):
        if not line.strip():
            cleaned_lines.append(line)
            continue
            
        # Enhanced OCR artifact cleanup
        cleaned_line = line
        
        # Remove PyMuPDF processing codes (patterns from raw data analysis)
        cleaned_line = re.sub(r'\d{3}\|\d{8}\|\d+\|\d{5}\|\d+\|[A-Z0-9~]+\|[A-Z0-9~]*\|\|\d+', '', cleaned_line)
        cleaned_line = re.sub(r'[A-Z0-9]{3}\d{5}~~\|[A-Z0-9]{3}\d{5}~~\|\|\d+', '', cleaned_line)
        cleaned_line = re.sub(r'[A-Z0-9]{3}\d{5}~~\|', '', cleaned_line)
        cleaned_line = re.sub(r'\|\|\d+\s*$', '', cleaned_line)
        
        # Fix OCR spacing issues (universal patterns)
        cleaned_line = re.sub(r'ACCOUNTS\s*PAYABLE(\d)', r'ACCOUNTS PAYABLE \1', cleaned_line)
        cleaned_line = re.sub(r'PAYABLE(\d)', r'PAYABLE \1', cleaned_line)
        cleaned_line = re.sub(r'PAYABLE([A-Z])', r'PAYABLE \1', cleaned_line)
        cleaned_line = re.sub(r'([a-z])([A-Z][a-z])', r'\1 \2', cleaned_line)
        
        # Fix address formatting
        cleaned_line = re.sub(r'(\d+)\s*([A-Z][A-Z\s]+ST)\s*([A-Z][A-Z\s]+),\s*([A-Z]{2})\s*(\d{5})', 
                            r'\1 \2 \3, \4 \5', cleaned_line)
        
        # Normalize dots and spacing in contact info (universal patterns)
        cleaned_line = re.sub(r'Phone:\s*\.\s*\.\s*\.\s*[.]*\s*', 'Phone: ', cleaned_line)
        cleaned_line = re.sub(r'Email:\s*\.\s*\.\s*\.\s*[.]*\s*', 'Email: ', cleaned_line)
        cleaned_line = re.sub(r'Visit Us:\s*\.\s*\.\s*\.\s*[.]*\s*', 'Visit Us: ', cleaned_line)
        
        # Remove excessive dots/underscores
        cleaned_line = re.sub(r'\.{4,}', ' ', cleaned_line)
        cleaned_line = re.sub(r'_{4,}', ' ', cleaned_line)
        
        # Fix broken words and formatting
        cleaned_line = re.sub(r'Ad\s*j\s*ustments', 'Adjustments', cleaned_line)
        cleaned_line = re.sub(r'Surchar\s*g\s*es', 'Surcharges', cleaned_line)
        cleaned_line = re.sub(r'(\$\d+\.?\d*)\s*\$', r'\1', cleaned_line)  # Remove duplicate dollar signs
        
        # Normalize account number formats
        cleaned_line = re.sub(r'Account\s*Number:\s*([A-Z0-9]+)', r'Account Number: \1', cleaned_line)
        cleaned_line = re.sub(r'Invoice\s*Number:\s*(\d+)', r'Invoice Number: \1', cleaned_line)
        
        # Clean whitespace but preserve structure
        cleaned_line = re.sub(r'\s+', ' ', cleaned_line)
        cleaned_line = cleaned_line.strip()
        
        if cleaned_line:  # Only add non-empty lines
            cleaned_lines.append(cleaned_line)
    
    return '\n'.join(cleaned_lines)

def remove_repeated_sections(text):
    """
    Remove repeated sections using generic patterns (addresses, headers, etc.)
    """
    if not text:
        return ""
    
    # Find repeated blocks (3+ lines that appear multiple times)
    lines = text.splitlines()
    block_counts = {}
    
    # Look for 3-line blocks that repeat
    for i in range(len(lines) - 2):
        block = '\n'.join(lines[i:i+3]).strip()
        if len(block) > 20:  # Only consider substantial blocks
            block_normalized = re.sub(r'\s+', ' ', block.lower())
            block_counts[block_normalized] = block_counts.get(block_normalized, 0) + 1
    
    # Remove repeated blocks (keep only first occurrence)
    for block_norm, count in block_counts.items():
        if count > 1:
            # Find the original format of the first occurrence
            for i in range(len(lines) - 2):
                test_block = '\n'.join(lines[i:i+3]).strip()
                test_normalized = re.sub(r'\s+', ' ', test_block.lower())
                if test_normalized == block_norm:
                    # Remove subsequent occurrences
                    text = text.replace(test_block, '', count - 1)
                    break
    
    return text

def clean_ocr_formatting_artifacts(line):
    """
    Clean OCR formatting artifacts while preserving important content.
    """
    if not line:
        return ""
    
    # Remove excessive dots/periods often from OCR (but preserve decimal points)
    line = re.sub(r'(?<!\d)\.{4,}(?!\d)', ' ', line)  # Don't affect decimal numbers
    line = re.sub(r'_{4,}', ' ', line)
    
    # Clean up spacing around punctuation but preserve currency formatting
    line = re.sub(r'\s*\.\s*\.\s*\.\s*', ' ', line)
    
    # Preserve important currency and date patterns
    # Don't clean periods that are part of currency or decimals
    # Don't clean colons that are part of labels or times
    
    return line.strip()

def is_ocr_artifact_line(line):
    """
    Determine if a line is likely an OCR artifact and should be removed.
    """
    if not line:
        return True
    
    # Lines that are mostly punctuation/symbols
    if re.match(r'^[\.\s_\-\|]{10,}$', line):
        return True
    
    # Very short lines with just numbers (likely page numbers or artifacts)
    if len(line) <= 3 and line.isdigit():
        return True
    
    # Lines with excessive repetitive characters
    if re.search(r'(.)\1{10,}', line):  # Same character repeated 10+ times
        return True
    
    # Lines that are just formatting separators
    if re.match(r'^[-_=\*\|]{5,}$', line):
        return True
    
    return False

def remove_duplicated_content_blocks(text):
    """
    Remove larger duplicated content blocks using generic patterns.
    """
    if not text:
        return ""
    
    # Look for common service offering text patterns that get repeated
    service_patterns = [
        r'(provides?\s+a\s+full\s+suite\s+of.*?(?:service|products)\.)',
        r'((?:voice|data|cloud|wireless).*?(?:customers|business)\.)',
        r'(call\s+today.*?(?:products|services)\.)',
        r'(please\s+(?:visit|contact).*?(?:\.com|service)\.)',
    ]
    
    for pattern in service_patterns:
        matches = re.findall(pattern, text, flags=re.IGNORECASE | re.DOTALL)
        for match in matches:
            if len(match) > 50:  # Only for substantial blocks
                # Count occurrences and remove duplicates
                count = text.lower().count(match.lower())
                if count > 1:
                    # Replace all but the first occurrence
                    text = text.replace(match, '', count - 1)
    
    return text

def preprocess_pymupdf_text(text):
    """Preprocess PyMuPDF extracted text with all sanitization steps."""
    if not text:
        return ""
    
    # Apply OCR-specific corrections first
    processed_text = correct_ocr_errors(text)
    processed_text = fix_concatenated_words(processed_text)
    processed_text = clean_ocr_artifacts(processed_text)
    processed_text = clean_generic_artifacts(processed_text)
    processed_text = reconstruct_broken_words(processed_text)
    processed_text = normalize_table_formatting(processed_text)
    
    # Original preprocessing logic
    lines = processed_text.splitlines()
    cleaned_lines = []
    for line in lines:
        temp_line = line.strip()
        temp_line = re.sub(r'^\s*(?:page|pg\.?)\s*\d+\s*(?:of\s*\d+)?\s*$', '', temp_line, flags=re.IGNORECASE)
        temp_line = re.sub(r'^\s*\d+\s*/\s*\d+\s*$', '', temp_line.strip())
        temp_line = re.sub(r'\s*(?:page|pg\.?)\s*\d+\s*(?:of\s*\d+)?\s*$', '', temp_line.strip(), flags=re.IGNORECASE)
        temp_line = re.sub(r'\s*\d+\s*/\s*\d+\s*$', '', temp_line.strip())
        if not temp_line:
            continue
        cleaned_lines.append(temp_line)
    processed_text = "\n".join(cleaned_lines)
    processed_text = re.sub(r'^\s*[-_=\*]{10,}\s*$', '', processed_text, flags=re.MULTILINE)
    
    # Apply structure-aware cleanup
    processed_text = structure_aware_cleanup(processed_text)
    
    return processed_text.strip()

def comprehensive_ocr_sanitization(text, apply_intelligent_boilerplate=True):
    """Comprehensive text sanitization pipeline."""
    if not text:
        return ""
    
    sanitized_text = text
    
    # Step 1: Basic OCR corrections
    sanitized_text = correct_ocr_errors(sanitized_text)
    sanitized_text = fix_concatenated_words(sanitized_text)
    sanitized_text = clean_ocr_artifacts(sanitized_text)
    sanitized_text = reconstruct_broken_words(sanitized_text)
    
    # Step 2: Structure improvements
    sanitized_text = normalize_table_formatting(sanitized_text)
    sanitized_text = structure_aware_cleanup(sanitized_text)
    
    # Step 3: Remove boilerplate/marketing content
    if apply_intelligent_boilerplate:
        sanitized_text = intelligent_boilerplate_removal(sanitized_text)
    
    # Step 4: Final cleanup
    sanitized_text = clean_generic_artifacts(sanitized_text)
    
    # Final normalization
    sanitized_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', sanitized_text)
    sanitized_text = re.sub(r'[ \t]+', ' ', sanitized_text)
    
    return sanitized_text.strip()

def sanitize_mistral_invoice_text(text):
    """
    Sanitize Mistral-generated invoice text by preserving legitimate content 
    and surgically removing only obvious fake/sample data.
    """
    if not text:
        return ""
    
    # Apply surgical fake data removal (preserves legitimate BCN invoice content)
    text = remove_obvious_fake_data_only(text)
    
    # Light cleanup of formatting issues without removing content
    # Remove excessive markdown formatting that might interfere with comparison
    text = re.sub(r'^#+\s*', '', text, flags=re.MULTILINE)  # Remove headers
    text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Remove bold formatting
    text = re.sub(r'\*([^*]+)\*', r'\1', text)  # Remove italic formatting
    
    # Normalize table formatting to be more comparable
    text = re.sub(r'\|\s*\|\s*\|\s*$', '', text, flags=re.MULTILINE)  # Remove empty table cells
    text = re.sub(r'^\s*\|\s*---\s*\|\s*---\s*\|\s*$', '', text, flags=re.MULTILINE)  # Remove table separators
    
    # Clean up whitespace while preserving structure
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
    text = re.sub(r'[ \t]+', ' ', text)  # Normalize spaces
    
    return text.strip()

def remove_obvious_fake_data_only(text):
    """
    Enhanced fake data removal - more aggressive detection of sample/fake invoice data
    while preserving all legitimate business document content.
    """
    import re
    
    lines = text.split('\n')
    cleaned_lines = []
    skip_line = False
    in_fake_section = False
    
    # Enhanced patterns for fake data detection
    fake_patterns = [
        # Sequential numbers and obvious test data
        r'\b123+4*5*6*7*8*9*0*\b',  # Various sequential patterns
        r'\bBOC00000+\b',  # Fake account numbers
        r'\bBON00000+\b',  # Fake account variants
        r'\$\d{2,3},\d{3}\.\d{4,}\b',  # Amounts with excessive decimals
        
        # Fake company variants and corrupted names
        r'\bBON\s+Telecom\b',  # Wrong company name (should be BCN)
        r'\bBON\s+List\b',  # Marketing fake content
        r'\bBON\s+Website\b',  # Marketing fake content
        
        # Large suspicious amounts (lowered threshold)
        r'\$[1-9]\d{4,},\d{3}\.\d{2}\b',  # Amounts > $10,000
        r'\$[5-9]\d{4,}\.\d{2}\b',  # Amounts > $50,000
        
        # Obvious marketing/sample content
        r'brand\s+evolution',
        r'corporate\s+website',
        r'multi-product\s+aggregation',
        r'wholesale\s+network\s+partners',
        r'state-of-the-art\s+portal',
        r'powerful\s+ally\s+in\s+finding',
        r'ultimate\s+user-friendly\s+experience',
        r'comprehensive\s+understanding',
        r'complex\s+technology\s+deployments',
        
        # Fake invoice indicators
        r'!\[img-\d+\.\w+\]',  # Image references in markdown
        r'continues\s+brand\s+evolution',
    ]
    
    # Compile patterns for efficiency
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in fake_patterns]
    
    # Track legitimate vs fake invoice data
    legitimate_account = None
    legitimate_invoice = None
    legitimate_amount = None
    
    # First pass: identify legitimate invoice data
    for line in lines:
        # Look for consistent account numbers
        account_match = re.search(r'Account.*?([A-Z]{3}\d{5})', line, re.IGNORECASE)
        if account_match and not any(pattern.search(line) for pattern in compiled_patterns):
            if legitimate_account is None:
                legitimate_account = account_match.group(1)
        
        # Look for consistent invoice numbers  
        invoice_match = re.search(r'Invoice.*?(\d{8})', line, re.IGNORECASE)
        if invoice_match and not any(pattern.search(line) for pattern in compiled_patterns):
            if legitimate_invoice is None:
                legitimate_invoice = invoice_match.group(1)
        
        # Look for consistent amounts
        amount_match = re.search(r'\$(\d{1,3},?\d{3}\.\d{2})', line)
        if amount_match and not any(pattern.search(line) for pattern in compiled_patterns):
            amount_value = float(amount_match.group(1).replace(',', ''))
            if amount_value < 10000:  # Reasonable invoice amount
                if legitimate_amount is None:
                    legitimate_amount = amount_match.group(1)
    
    # Second pass: filter content
    current_table_lines = []
    in_table = False
    table_has_fake_data = False
    skip_until_next_section = False
    
    for i, line in enumerate(lines):
        original_line = line
        
        # Detect table start/end
        if '|' in line and '---' in line:
            in_table = True
            current_table_lines = [line]
            table_has_fake_data = False
            continue
        elif in_table:
            if '|' in line:
                current_table_lines.append(line)
                # Check if this table line contains fake data
                if any(pattern.search(line) for pattern in compiled_patterns):
                    table_has_fake_data = True
                # Check for inconsistent data
                if legitimate_account and legitimate_account not in line and re.search(r'[A-Z]{3}\d{5}', line):
                    table_has_fake_data = True
                if legitimate_invoice and legitimate_invoice not in line and re.search(r'\d{8}', line):
                    table_has_fake_data = True
                continue
            else:
                # End of table - decide whether to keep it
                if not table_has_fake_data:
                    cleaned_lines.extend(current_table_lines)
                current_table_lines = []
                in_table = False
                table_has_fake_data = False
        
        # Skip if we're in a table (handled above)
        if in_table:
            continue
            
        # Check for section breaks that indicate fake content
        if skip_until_next_section:
            # Look for clear section breaks
            if (line.strip() == '' or 
                re.match(r'^#{1,6}\s', line) or  # Markdown headers
                re.match(r'^[A-Z][A-Z\s]{10,}$', line) or  # ALL CAPS headers
                'Account Information' in line or
                'Invoice Information' in line or
                'Billing Summary' in line):
                skip_until_next_section = False
            else:
                continue
        
        # Enhanced fake data detection
        line_has_fake_data = False
        
        # Check against all fake patterns
        for pattern in compiled_patterns:
            if pattern.search(line):
                line_has_fake_data = True
                break
        
        # Check for inconsistent invoice data
        if legitimate_account:
            # If line has account number but it's not the legitimate one
            if re.search(r'Account.*?[A-Z]{3}\d{5}', line, re.IGNORECASE):
                if legitimate_account not in line:
                    line_has_fake_data = True
        
        if legitimate_invoice:
            # If line has invoice number but it's not the legitimate one
            if re.search(r'Invoice.*?\d{8}', line, re.IGNORECASE):
                if legitimate_invoice not in line:
                    line_has_fake_data = True
        
        # Check for multiple conflicting billing summaries
        if 'Total Amount Due:' in line:
            amount_in_line = re.search(r'\$(\d{1,3}(?:,\d{3})*\.\d{2})', line)
            if amount_in_line and legitimate_amount:
                if amount_in_line.group(1) != legitimate_amount:
                    line_has_fake_data = True
        
        # Detect fake invoice sections by large amounts
        large_amount_match = re.search(r'\$(\d{2,3},\d{3}\.\d{2})', line)
        if large_amount_match:
            amount_value = float(large_amount_match.group(1).replace(',', ''))
            if amount_value > 10000:  # Suspiciously large amounts
                line_has_fake_data = True
                skip_until_next_section = True
        
        # Check for obviously fake sequential invoice numbers
        if re.search(r'*********', line):
            line_has_fake_data = True
            skip_until_next_section = True
        
        # Check for wrong company names
        if re.search(r'\bBON\s+Telecom\b', line, re.IGNORECASE):
            line_has_fake_data = True
            skip_until_next_section = True
        
        # Preserve legitimate business content
        if not line_has_fake_data:
            # Clean up minor OCR artifacts while preserving content
            cleaned_line = line
            
            # Fix common OCR spacing issues without losing data
            cleaned_line = re.sub(r'PAYABLE(\d)', r'PAYABLE \1', cleaned_line)
            cleaned_line = re.sub(r'([A-Z])(\d{5})', r'\1 \2', cleaned_line)  # Fix account number spacing
            
            cleaned_lines.append(cleaned_line)
    
    # Final cleanup - remove any remaining fake marketing content
    final_lines = []
    skip_marketing_block = False
    
    for line in cleaned_lines:
        # Detect start of marketing blocks
        if any(phrase in line.lower() for phrase in [
            'brand evolution', 'corporate website', 'user-friendly experience',
            'wholesale network partners', 'multi-product aggregation',
            'state-of-the-art portal', 'powerful ally'
        ]):
            skip_marketing_block = True
            continue
        
        # Detect end of marketing blocks
        if skip_marketing_block:
            if (line.strip() == '' or 
                any(phrase in line for phrase in [
                    'Account Information', 'Service Category', 'Product Summary',
                    'Transactions Report', 'Location Charges'
                ])):
                skip_marketing_block = False
            else:
                continue
        
        final_lines.append(line)
    
    result = '\n'.join(final_lines)
    
    # Remove excessive blank lines
    result = re.sub(r'\n{3,}', '\n\n', result)
    
    return result.strip()

def remove_duplicate_billing_sections(text):
    """
    Remove duplicate billing summary sections, keeping the most complete one.
    """
    if not text:
        return ""
    
    # Find all billing summary sections
    billing_pattern = r'(billing\s+summary.*?(?=\n\s*\n|\Z))'
    billing_sections = re.findall(billing_pattern, text, flags=re.IGNORECASE | re.DOTALL)
    
    if len(billing_sections) <= 1:
        return text
    
    # Score each section by completeness (number of currency amounts)
    best_section = ""
    best_score = 0
    
    for section in billing_sections:
        # Count currency amounts as a completeness metric
        amounts = re.findall(r'\$\s*\d+(?:,\d{3})*(?:\.\d{2})?', section)
        score = len(amounts)
        
        # Prefer sections with realistic amounts (under $50k)
        realistic_amounts = [a for a in amounts if not re.search(r'\$\s*[5-9]\d{4,}', a)]
        if len(realistic_amounts) == len(amounts):
            score += 10  # Bonus for all realistic amounts
        
        if score > best_score:
            best_score = score
            best_section = section
    
    # Replace all billing sections with the best one
    if best_section:
        # Remove all billing sections first
        text_no_billing = re.sub(billing_pattern, '', text, flags=re.IGNORECASE | re.DOTALL)
        # Add back the best section
        text = best_section + '\n\n' + text_no_billing
    
    return text

def normalize_company_names(text):
    """
    Fix common company name inconsistencies in a generic way.
    """
    if not text:
        return ""
    
    # Find the most common company name pattern and standardize to it
    company_patterns = [
        (r'\b([A-Z]{2,4})\s*Telecom(?:\s+Inc)?\b', r'\1 Telecom'),
        (r'\b([A-Z]{2,4})\s*Technologies?(?:\s+Inc)?\b', r'\1 Technology'),
        (r'\b([A-Z]{2,4})\s*Communications?(?:\s+Inc)?\b', r'\1 Communications'),
        (r'\b([A-Z]{2,4})\s*Systems?(?:\s+Inc)?\b', r'\1 Systems'),
    ]
    
    for pattern, replacement in company_patterns:
        # Find all matches and standardize to the most common form
        matches = re.findall(pattern, text, flags=re.IGNORECASE)
        if matches:
            most_common = max(set(matches), key=matches.count)
            # Replace variations with the most common form
            standardized = replacement.replace(r'\1', most_common)
            text = re.sub(pattern, standardized, text, flags=re.IGNORECASE)
    
    return text

def remove_generic_marketing_content(text):
    """
    Remove common marketing/boilerplate content patterns.
    """
    if not text:
        return ""
    
    marketing_patterns = [
        # Brand evolution and marketing content
        r'(?i).*(?:brand\s+evolution|website.*continues|completely\s+redesigned).*?(?=\n\s*\n|\Z)',
        r'(?i).*(?:finding\s+simplicity|powerful\s+ally).*?(?=\n\s*\n|\Z)',
        r'(?i).*(?:portfolio\s+of.*partners|wholesale\s+network).*?(?=\n\s*\n|\Z)',
        
        # Generic service descriptions that are likely marketing
        r'(?i).*(?:array\s+of.*solutions|transforming.*into.*solutions).*?(?=\n\s*\n|\Z)',
        r'(?i).*(?:superior\s+reliability|future[_\s]proof).*?(?=\n\s*\n|\Z)',
        
        # Remove lines with obvious sample/demo content
        r'(?i).*(?:sample|demo|test|example).*invoice.*?(?=\n\s*\n|\Z)',
    ]
    
    for pattern in marketing_patterns:
        text = re.sub(pattern, '', text, flags=re.MULTILINE | re.DOTALL)
    
    return text 