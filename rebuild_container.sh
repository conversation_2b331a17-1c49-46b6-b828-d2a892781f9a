#!/bin/bash

# PyMuPDF Flask API - Container Rebuild Script
# This script removes existing containers and images, then rebuilds and runs the application

set -e  # Exit on any error

# Configuration
CONTAINER_NAME="PyMuPDF-container"
IMAGE_NAME="pymupdf-api"
HOST_PORT="5001"
CONTAINER_PORT="5000"

echo "🚀 Starting PyMuPDF Flask API container rebuild..."
echo "================================================"

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Error: Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to stop and remove container
cleanup_container() {
    echo "🔍 Checking for existing container: $CONTAINER_NAME"
    
    # Check if container exists (running or stopped)
    if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        echo "🛑 Stopping container: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" 2>/dev/null || echo "   Container was already stopped"
        
        echo "🗑️  Removing container: $CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
        echo "   ✅ Container removed successfully"
    else
        echo "   ℹ️  No existing container found"
    fi
}

# Function to remove image
cleanup_image() {
    echo "🔍 Checking for existing image: $IMAGE_NAME"
    
    if docker images --format "table {{.Repository}}" | grep -q "^$IMAGE_NAME$"; then
        echo "🗑️  Removing image: $IMAGE_NAME"
        docker rmi "$IMAGE_NAME"
        echo "   ✅ Image removed successfully"
    else
        echo "   ℹ️  No existing image found"
    fi
}

# Function to build new image
build_image() {
    echo "🏗️  Building new Docker image: $IMAGE_NAME"
    docker build -t "$IMAGE_NAME" .
    echo "   ✅ Image built successfully"
}

# Function to run new container
run_container() {
    echo "🚢 Running new container: $CONTAINER_NAME"
    docker run -d -p "$HOST_PORT:$CONTAINER_PORT" --name "$CONTAINER_NAME" "$IMAGE_NAME"
    echo "   ✅ Container started successfully"
    echo "   🌐 Application available at: http://localhost:$HOST_PORT"
}

# Function to display container status
show_status() {
    echo ""
    echo "📊 Container Status:"
    echo "==================="
    docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "📊 Image Info:"
    echo "=============="
    docker images --filter "reference=$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# Function to cleanup dangling images
cleanup_dangling() {
    echo "🧹 Cleaning up dangling images..."
    dangling_images=$(docker images -f "dangling=true" -q)
    if [ -n "$dangling_images" ]; then
        docker rmi $dangling_images 2>/dev/null || echo "   No dangling images to remove"
        echo "   ✅ Dangling images cleaned up"
    else
        echo "   ℹ️  No dangling images found"
    fi
}

# Main execution
main() {
    check_docker
    cleanup_container
    cleanup_image
    cleanup_dangling
    echo ""
    build_image
    echo ""
    run_container
    echo ""
    show_status
    echo ""
    echo "🎉 Rebuild complete! Your PyMuPDF Flask API is ready."
    echo "📝 To view logs: docker logs $CONTAINER_NAME"
    echo "🛑 To stop: docker stop $CONTAINER_NAME"
}

# Run the main function
main 