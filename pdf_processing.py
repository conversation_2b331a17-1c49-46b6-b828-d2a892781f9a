import fitz  # PyMuPDF
import pymupdf4llm # For Markdown conversion
import io
import logging

# Configure logging for this module
logger = logging.getLogger(__name__)

def make_serializable(obj):
    """
    Recursively converts PyMuPDF geometric objects (Rect, Point, Matrix)
    in a data structure to JSON-serializable formats.
    """
    if isinstance(obj, dict):
        return {k: make_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [make_serializable(elem) for elem in obj]
    elif isinstance(obj, fitz.Rect):
        return {"x0": obj.x0, "y0": obj.y0, "x1": obj.x1, "y1": obj.y1, "_type": "fitz.Rect"}
    elif isinstance(obj, fitz.Point):
        return {"x": obj.x, "y": obj.y, "_type": "fitz.Point"}
    elif isinstance(obj, fitz.Matrix):
        return list(obj)
    else:
        return obj

# --- Configuration for PDF processing ---
MIN_TEXT_THRESHOLD_DEFAULT = 20
DPI_FOR_OCR_DEFAULT = 300
OCR_LANGUAGE_DEFAULT = "eng"

def extract_text_from_pdf(pdf_bytes, min_text_threshold=None, dpi_for_ocr=None, ocr_language=None):
    """
    Extract text from PDF using PyMuPDF with OCR fallback for low-text pages.
    """
    if min_text_threshold is None:
        min_text_threshold = MIN_TEXT_THRESHOLD_DEFAULT
    if dpi_for_ocr is None:
        dpi_for_ocr = DPI_FOR_OCR_DEFAULT
    if ocr_language is None:
        ocr_language = OCR_LANGUAGE_DEFAULT

    try:
        original_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        processed_doc = fitz.open()
        
        logger.info(f"Processing PDF with {original_doc.page_count} pages. OCR: {min_text_threshold} chars, DPI: {dpi_for_ocr}, Lang: {ocr_language}")
        
        for page_num, page in enumerate(original_doc):
            existing_text = page.get_text().strip()
            if len(existing_text) < min_text_threshold:
                logger.info(f"Page {page_num + 1}: Minimal text. OCRing.")
                pix = None; ocr_page_doc_temp = None
                try:
                    pix = page.get_pixmap(dpi=dpi_for_ocr)
                    ocr_page_doc_temp = fitz.open()
                    ocr_pdf_bytes = pix.pdfocr_tobytes(language=ocr_language, tessdata=None)
                    if ocr_pdf_bytes:
                        ocr_page_doc_temp.insert_pdf(fitz.open(stream=ocr_pdf_bytes, filetype="pdf"))
                        logger.info(f"Page {page_num + 1}: OCR layer added.")
                        processed_doc.insert_pdf(ocr_page_doc_temp)
                    else:
                        logger.warning(f"Page {page_num + 1}: pdfocr_tobytes() empty. Copying original.")
                        processed_doc.insert_pdf(original_doc, from_page=page.number, to_page=page.number)
                except Exception as e:
                    logger.error(f"Page {page_num + 1}: OCR failed: {e}. Copying original page.")
                    processed_doc.insert_pdf(original_doc, from_page=page.number, to_page=page.number)
                finally:
                    if pix: pix = None
                    if ocr_page_doc_temp: ocr_page_doc_temp.close(); ocr_page_doc_temp = None
            else:
                logger.info(f"Page {page_num + 1}: Sufficient text. Copying original.")
                processed_doc.insert_pdf(original_doc, from_page=page.number, to_page=page.number)
        
        return processed_doc, original_doc
        
    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise

def convert_pdf_to_markdown(processed_doc):
    """
    Convert processed PDF document to markdown using PyMuPDF4LLM.
    """
    try:
        # Pass the document object directly to pymupdf4llm, not bytes
        markdown_chunks_raw = pymupdf4llm.to_markdown(doc=processed_doc, page_chunks=True)
        logger.info(f"Converted to {len(markdown_chunks_raw)} Markdown chunks.")

        # Make the result JSON serializable
        serializable_markdown_chunks = make_serializable(markdown_chunks_raw)
        return serializable_markdown_chunks
    except Exception as e:
        logger.error(f"Error converting PDF to markdown: {e}")
        raise