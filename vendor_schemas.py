import json
import logging
from flask import Response

# Configure logging for this module
logger = logging.getLogger(__name__)

# Vendor schemas storage (in production, this would likely be in a database)
vendor_schemas_store = [
    {
        "vendor_name": "BCN Telecom",
        "fields": [
            {"name": "account_number", "type": "string", "required": True},
            {"name": "invoice_number", "type": "string", "required": True},
            {"name": "invoice_date", "type": "date", "required": True},
            {"name": "due_date", "type": "date", "required": True},
            {"name": "total_amount", "type": "currency", "required": True},
            {"name": "customer_name", "type": "string", "required": False},
            {"name": "billing_address", "type": "string", "required": False},
            {"name": "line_items", "type": "array", "required": False}
        ]
    },
    {
        "vendor_name": "Generic Telecom",
        "fields": [
            {"name": "account_id", "type": "string", "required": True},
            {"name": "invoice_id", "type": "string", "required": True},
            {"name": "bill_date", "type": "date", "required": True},
            {"name": "amount_due", "type": "currency", "required": True},
            {"name": "service_charges", "type": "currency", "required": False},
            {"name": "taxes", "type": "currency", "required": False}
        ]
    }
]

def make_serializable(obj):
    """Convert objects to JSON-serializable format."""
    if hasattr(obj, '__dict__'):
        return {key: make_serializable(value) for key, value in obj.__dict__.items()}
    elif isinstance(obj, list):
        return [make_serializable(item) for item in obj]
    elif isinstance(obj, dict):
        return {key: make_serializable(value) for key, value in obj.items()}
    else:
        return obj

def get_all_vendor_schemas():
    """Get all vendor schemas."""
    logger.info(f"Request received for all vendor schemas. Returning {len(vendor_schemas_store)} schemas.")
    return vendor_schemas_store

def get_vendor_schema_by_name(vendor_name):
    """Get vendor schema by name."""
    logger.info(f"Request received for vendor schema: {vendor_name}")
    
    for schema in vendor_schemas_store:
        if schema.get("vendor_name", "").lower() == vendor_name.lower():
            return schema
    
    logger.warning(f"Vendor schema not found for: {vendor_name}")
    return None

def generate_vendor_schemas_sse():
    """Generate Server-Sent Events stream for vendor schemas."""
    logger.info("SSE connection established for vendor schemas (finite stream - no heartbeat).")
    
    def generate_schema_events():
        try:
            for schema_obj in vendor_schemas_store:
                serialized_schema = make_serializable(schema_obj)
                yield f"data: {json.dumps(serialized_schema)}\n\n"
                logger.debug(f"Sent schema for {schema_obj.get('vendor_name', 'Unknown Vendor')} via SSE.")
        except Exception as e:
            error_data = {"error": f"SSE streaming error: {str(e)}"}
            yield f"data: {json.dumps(error_data)}\n\n"
            logger.error(f"Error in SSE vendor schemas streaming: {e}")
        finally:
            logger.info("All vendor schemas sent via SSE. Stream will now end (finite stream).")
    
    return Response(generate_schema_events(), content_type='text/event-stream')

def add_vendor_schema(vendor_name, fields):
    """Add a new vendor schema (for future extensibility)."""
    new_schema = {
        "vendor_name": vendor_name,
        "fields": fields
    }
    vendor_schemas_store.append(new_schema)
    logger.info(f"Added new vendor schema for: {vendor_name}")
    return new_schema

def update_vendor_schema(vendor_name, updated_fields):
    """Update an existing vendor schema (for future extensibility)."""
    for i, schema in enumerate(vendor_schemas_store):
        if schema.get("vendor_name", "").lower() == vendor_name.lower():
            vendor_schemas_store[i]["fields"] = updated_fields
            logger.info(f"Updated vendor schema for: {vendor_name}")
            return vendor_schemas_store[i]
    
    logger.warning(f"Vendor schema not found for update: {vendor_name}")
    return None 