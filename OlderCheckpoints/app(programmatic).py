# PROGRAMMATIC APPROACH TO PARSING INVOICES


from flask import Flask, request, jsonify
import fitz  # PyMuPDF
import re
from jsonschema import validate, ValidationError
from collections import OrderedDict

app = Flask(__name__)

# Schema remains the same as your target structure for Services_detail (array of objects)
DOCUMENT_ANNOTATION_SCHEMA = {
    "type": "object",
    "title": "DocumentAnnotation",
    "properties": {
        "Vendor Name": {"type": ["string", "null"]},
        "Invoice Number": {"type": ["string", "null"]},
        "Customer Number": {"type": ["string", "null"]},
        "Current Charges": {"type": ["string", "null"]},
        "Finance Charges": {"type": ["string", "null"]},
        "Invoice Date": {"type": ["string", "null"]},
        "Current Charges Due (USD)": {"type": ["string", "null"]},
        "Services_detail": {
            "title": "Services_detail",
            "type": "array",
            "items": {
                "type": "object",
                "additionalProperties": {
                    "type": "object",
                    "additionalProperties": {"type": "string"}
                }
            }
        }
    },
    "required": ["Vendor Name", "Invoice Number", "Invoice Date", "Services_detail"],
    "additionalProperties": False
}

def clean_value(value_str):
    if value_str is None:
        return None
    return value_str.replace('$', '').replace(',', '').replace('\n', ' ').strip()

def parse_pdf_to_document_annotation(pdf_document):
    parsed_data = OrderedDict([
        ("Vendor Name", None),
        ("Invoice Number", None),
        ("Customer Number", None),
        ("Invoice Date", None),
        ("Current Charges", None),
        ("Finance Charges", None),
        ("Current Charges Due (USD)", None),
        ("Services_detail", [])
    ])

    full_text_for_header_parsing = ""
    text_by_page = []
    for page_num in range(len(pdf_document)):
        page = pdf_document.load_page(page_num)
        page_text = page.get_text("text")
        full_text_for_header_parsing += page_text + "\n"
        text_by_page.append(page_text)

    # --- 1. Enhanced Header Field Extraction ---
    
    # Enhanced vendor detection - check for multiple vendor patterns
    vendor_patterns = [
        ("BCN", ["BCN Telecom", "BCN", "bcntele.com"]),
        ("GTT", ["GC Pivotal LLC", "GTT Communications", "GTT", "Global Telecom"]),
        ("Verizon", ["Verizon", "VZ", "Verizon Business", "Verizon Wireless"]),
        ("AT&T", ["AT&T", "ATT", "AT & T", "American Telephone"]),
        ("Comcast", ["Comcast", "Xfinity", "Comcast Business"]),
        ("CenturyLink", ["CenturyLink", "Century Link", "Lumen", "Level 3"])
    ]
    
    for vendor_name, patterns in vendor_patterns:
        for pattern in patterns:
            if pattern.lower() in full_text_for_header_parsing.lower():
                parsed_data["Vendor Name"] = vendor_name
                break
        if parsed_data["Vendor Name"]:
            break

    # Enhanced header field patterns with more comprehensive date formats
    header_field_patterns = {
        "Customer Number": [
            r"Account\s+Number\s*[:\n]\s*([A-Z0-9]+)",  # BCN format
            r"Customer\s+(?:Number|No|ID|#)\s*[:\n]\s*([A-Z0-9]+)",
            r"Cust(?:omer)?\s+(?:Number|No|ID|#)\s*[:\n]\s*([A-Z0-9]+)"
        ],
        "Invoice Number": [
            r"Invoice\s+(?:Number|No|ID|#)\s*[:\n]\s*([A-Z0-9]+)",
            r"Inv(?:oice)?\s+(?:Number|No|ID|#)\s*[:\n]\s*([A-Z0-9]+)",
            r"Bill\s+(?:Number|No|ID|#)\s*[:\n]\s*([A-Z0-9]+)"
        ],
        "Invoice Date": [
            r"Invoice\s+Date\s*[:\n]\s*(\d{1,2}\/\d{1,2}\/\d{4})",  # BCN format: 5/1/2025
            r"Invoice\s+Date\s*[:\n]\s*(\d{1,2}[-\/]\w{3}[-\/]\d{2,4})",  # GTT format: 01-May-25
            r"Invoice\s+Date\s*[:\n]\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})",
            r"Bill\s+Date\s*[:\n]\s*(\d{1,2}[-\/]\w{3}[-\/]\d{2,4})",
            r"Date\s*[:\n]\s*(\d{1,2}[-\/]\w{3}[-\/]\d{2,4})",
            r"Date\s*[:\n]\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})"
        ],
        "Current Charges": [
            r"Total\s+Current\s+Charges\s*[:\n]\s*\$?([\d\.,]+)",  # BCN format
            r"Service\s+Charges\s*[:\n]\s*\$?([\d\.,]+)",  # BCN format
            r"Current\s+Charges\s*[:\n]\s*\$?([\d\.,]+)",
            r"Total\s+(?:Charges|Amount)\s*[:\n]\s*\$?([\d\.,]+)",
            r"Amount\s+Due\s*[:\n]\s*\$?([\d\.,]+)",
            r"Balance\s+Due\s*[:\n]\s*\$?([\d\.,]+)"
        ],
        "Finance Charges": [
            r"Finance\s+Charge\s*[:\n]\s*\$?([\d\.,]+)",  # BCN format
            r"Finance\s+Charges?\s*[:\n]\s*\$?([\d\.,]+)",
            r"Late\s+(?:Charges?|Fees?)\s*[:\n]\s*\$?([\d\.,]+)",
            r"Interest\s+Charges?\s*[:\n]\s*\$?([\d\.,]+)"
        ],
        "Current Charges Due (USD)": [
            r"Total\s+Amount\s+Due\s*[:\n]\s*\$?([\d\.,]+)",  # BCN format
            r"Amount\s+Due\s*[:\n]\s*\$?([\d\.,]+)",  # BCN format
            r"Total\s+Charges\s+Due\s+\(USD\)\s*[:\n]\s*\$?([\d\.,]+)",
            r"Current\s+Charges\s+Due\s+\(USD\)\s*[:\n]\s*\$?([\d\.,]+)",
            r"Total\s+Due\s+\(USD\)\s*[:\n]\s*\$?([\d\.,]+)",
            r"Balance\s+Due\s+\(USD\)\s*[:\n]\s*\$?([\d\.,]+)"
        ]
    }
    
    # Try each pattern variation for each field
    for field_name, patterns in header_field_patterns.items():
        for pattern in patterns:
            match = re.search(pattern, full_text_for_header_parsing, re.IGNORECASE | re.MULTILINE)
            if match:
                parsed_data[field_name] = clean_value(match.group(1))
                break
        if parsed_data[field_name]:  # Stop trying patterns once we find a match
            continue

    if parsed_data["Finance Charges"] is None:
        parsed_data["Finance Charges"] = "0.00"
    if parsed_data["Current Charges"] is None and parsed_data["Current Charges Due (USD)"] is not None:
        parsed_data["Current Charges"] = parsed_data["Current Charges Due (USD)"]

    # --- 2. Universal Services_detail Extraction ---
    # Scan all pages comprehensively to catch ANY line items
    services = []
    processed_lines = set()
    
    for page_num in range(len(text_by_page)):
        page_text = text_by_page[page_num]
        page_lines = [line.strip() for line in page_text.split('\n') if line.strip()]
        
        # Track current report section for context
        current_section = "General"
        
        for i, line in enumerate(page_lines):
            if line in processed_lines:
                continue
                
            # Detect report sections for context
            section_patterns = [
                ("Individual Products Report", r"Individual\s+Products?\s+Report"),
                ("Service Category Report", r"Service\s+Category\s+Report"),
                ("Taxes and Surcharges Summary", r"Taxes\s+and\s+Surcharges\s+Summary"),
                ("Location Charges Summary", r"Location\s+Charges\s+Summary"),
                ("Transactions Report", r"Transactions?\s+Report"),
                ("Product Summary Report", r"Product\s+Summary\s+Report")
            ]
            
            for section_name, pattern in section_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    current_section = section_name
                    break
                
            # Enhanced skip patterns for better noise filtering
            skip_patterns = [
                r"^page \d+", r"^Account Information", r"^Invoice Information", 
                r"^Billing Summary", r"^Contact Us", r"^Payment Coupon",
                r"^www\.", r"@", r"^Phone:", r"^Email:", r"^Visit Us:",
                r"^\d{3}[\|\-]\d{8}[\|\-]", r"^Please detach", r"^Please make checks",
                r"^For customers ready", r"^BCN has an array", r"^Talk with your",
                r"^Welcome to", r"^To learn more", r"^Please note that",
                r"^In addition", r"^Our service offering", r"^Call today",
                r"^How to read", r"^Summary of", r"^Customer Message", r"^Remittance",
                r"^HOTEL BARDO", r"^ACCOUNTS PAYABLE", r"^SAVANNAH, GA", r"^700 DRAYTON",
                r"^BCN Telecom", r"^P\.O\. Box", r"^Philadelphia, PA",
                r"remove.*payment", r"include.*payment", r"payable system",
                r"no action.*required", r"return envelope",
                
                # Marketing/Promotional content patterns
                r"offers? alternatives", r"deliver superior", r"cost-effective",
                r"future-proofed", r"cloud-enabled", r"solutions that deliver",
                r"transforming them into", r"superior reliability", r"performance",
                r"tailored to support", r"voice communications needs",
                r"match the right technology", r"applications and priorities",
                r"your organization", r"Trusted IT Advisor", r"Representative today",
                r"full suite of", r"business customers", r"service offering contains",
                r"latest in", r"telecom products", r"technology.*as well as",
                r"often valued", r"long standing", r"traditional telecom",
                r"learn more about", r"latest Hosted", r"Phone Selection",
                r"Private Data Network", r"Solutions and", r"Access Products",
                r"consider us for", r"all your", r"complete service offering",
                r"contact.*today", r"remittance address", r"has changed",
                r"return envelope", r"payables system", r"payment slip",
                
                # Structural/Label patterns
                r"^Invoice Number:?$", r"^Invoice Date:?$", r"^Customer Number:?$",
                r"^Account Number:?$", r"^Service Charges:?$", r"^Finance Charge:?$",
                r"^Current Charges:?$", r"^Total.*Charges:?$", r"^Due Date:?$",
                r"^Amount Due:?$", r"^Previous Balance:?$", r"^Payment Received:?$",
                r"^Taxes and Surcharges:?$", r"^Description:?$", r"^Location:?$",
                r"including.*Charges.*Taxes", r"resulting in a Grand", r"plus Taxes",
                r"and Surcharges\.", r"in the space provided", r"feel free",
                r"your invoice online", r"report$", r"summary$", r"total:?$"
            ]
            
            if any(re.search(pattern, line, re.IGNORECASE) for pattern in skip_patterns):
                continue
            
            # Additional marketing content detection
            marketing_indicators = [
                r"offers?.*solutions?", r"deliver.*performance", r"enable.*solutions?",
                r"transform.*into", r"cost-effective.*future", r"cloud.*solutions?",
                r"superior.*reliability", r"business.*needs", r"right technology",
                r"latest.*technology", r"complete.*offering", r"full suite",
                r"trusted.*advisor", r"representative.*today", r"learn more",
                r"contact.*today", r"please.*consider", r"in addition.*please"
            ]
            
            # Skip lines that look like marketing content
            if any(re.search(pattern, line, re.IGNORECASE) for pattern in marketing_indicators):
                continue
            
            # More precise service and amount detection
            item_type = "Service"
            description = None
            amount = None
            
            # Pattern 1: Service description with amount on same line
            same_line_match = re.search(r'^(.+?)\s+(\d+\.\d{2})$', line)
            if same_line_match:
                description = same_line_match.group(1).strip()
                amount = same_line_match.group(2)
            
            # Pattern 2: Service description followed by amount on next line
            elif re.search(r'(Internet|Cable|Phone|Voice|Data|Equipment|Modem|Router|Static|IP|Service|Charge|Tax|Fee|Surcharge|Recovery|Regulatory)', line, re.IGNORECASE):
                description = line.strip()
                
                # Look for amount in the next few lines (structured approach)
                for j in range(i + 1, min(i + 4, len(page_lines))):
                    next_line = page_lines[j].strip()
                    
                    # Skip empty lines and headers
                    if not next_line or any(re.search(skip, next_line, re.IGNORECASE) for skip in skip_patterns):
                        continue
                    
                    # Look for standalone amount
                    amount_match = re.search(r'^(\d+\.\d{2})$', next_line)
                    if amount_match:
                        amount = amount_match.group(1)
                        processed_lines.add(next_line)
                        break
                    
                    # Look for amount with some text
                    amount_with_text = re.search(r'(\d+\.\d{2})', next_line)
                    if amount_with_text:
                        # Only use if the line is short (likely just the amount with minimal context)
                        if len(next_line) <= 20:
                            amount = amount_with_text.group(1)
                            processed_lines.add(next_line)
                            break
                    
                    # Stop if we hit another service description
                    if re.search(r'(Internet|Cable|Phone|Voice|Data|Equipment|Modem|Router|Static|IP|Service|Charge)', next_line, re.IGNORECASE):
                        break
            
            # Pattern 3: Structured table parsing (BCN style)
            elif re.search(r'^\d{2}\/\d{2}\/\d{2}$', line):  # Date line like "05/01/25"
                # Look ahead for service description and amount
                if i + 2 < len(page_lines):
                    potential_desc = page_lines[i + 1].strip()
                    potential_amount_line = page_lines[i + 2].strip()
                    
                    # Check if this looks like a service item
                    if (re.search(r'(Internet|Cable|Phone|Voice|Data|Equipment|Modem|Router|Static|IP)', potential_desc, re.IGNORECASE) and
                        re.search(r'^\d+\.\d{2}$', potential_amount_line)):
                        description = potential_desc
                        amount = potential_amount_line
                        processed_lines.add(page_lines[i + 1])
                        processed_lines.add(potential_amount_line)
            
            # Pattern 4: Tax/Fee items with specific format
            elif re.search(r'(Tax|Fee|Surcharge|Recovery|Regulatory|Universal|Assessment|Fund|Compliance)', line, re.IGNORECASE):
                item_type = "Tax"
                description = line.strip()
                
                # Look for amount on same line first
                same_line_amount = re.search(r'(\d+\.\d{2})', line)
                if same_line_amount:
                    amount = same_line_amount.group(1)
                else:
                    # Look for amount in next line(s)
                    for j in range(i + 1, min(i + 3, len(page_lines))):
                        next_line = page_lines[j].strip()
                        
                        if re.search(r'^(\d+\.\d{2})$', next_line):
                            amount = next_line
                            processed_lines.add(next_line)
                            break
            
            # Pattern 5: Lines with direct amounts (currency indicators)
            elif re.search(r'\$(\d+(?:,\d{3})*\.?\d*)', line):
                amount_match = re.search(r'\$(\d+(?:,\d{3})*\.?\d*)', line)
                if amount_match:
                    description = re.sub(r'\$\d+(?:,\d{3})*\.?\d*', '', line).strip()
                    amount = amount_match.group(1).replace(',', '')
                    if not description:
                        description = line.strip()
            
            # Create service entry if we found a valid description
            if description and len(description) >= 3 and len(description) <= 200:
                # Clean up description
                description = re.sub(r'^(From\s+)?\d{1,2}\/\d{1,2}\/\d{4}(\s+thru\s+\d{1,2}\/\d{1,2}\/\d{4})?', '', description).strip()
                description = re.sub(r'^\d{2}\/\d{2}\/\d{2}\s*', '', description).strip()
                
                # Skip if description is too generic or just structural
                if (description and 
                    not re.match(r'^(Description|Amount|Date|Level|Total|Location|ProductID|Service Category|Voice|Wireless|Internet|Cable|Dedicated|Fixed)$', description, re.IGNORECASE) and
                    not re.match(r'^[\d\.\$\(\)\-\s,]+$', description) and
                    len(description.strip()) >= 3):
                    
                    # Include section context in the service key
                    service_key = f"{description}##{item_type}##{current_section}##page{page_num+1}##entry{len(services)+1}"
                    service_value = amount if amount else description
                    
                    services.append({service_key: {service_key: service_value}})
                    processed_lines.add(line)
    
    # Remove duplicates and prioritize Service Category Report over Individual Products
    filtered_services = []
    seen_services = {}  # Track service descriptions and their indices
    
    for service in services:
        for key, value in service.items():
            parts = key.split('##')
            service_desc = parts[0].lower()
            service_type = parts[1]
            service_section = parts[2] if len(parts) > 2 else "General"
            service_amount = list(value.values())[0]
            
            # Create a normalized key to detect duplicates
            desc_key = re.sub(r'[^\w\s]', '', service_desc.lower())[:50].strip()
            
            # Quality filter - keep meaningful services
            keep_item = (
                # Has a clear monetary amount
                (service_amount and re.match(r'^\d+\.?\d*$', str(service_amount))) or
                
                # Contains clear service keywords
                any(keyword in service_desc for keyword in [
                    'internet', 'cable', 'phone', 'voice', 'data', 'equipment', 
                    'modem', 'router', 'static', 'ip', 'recurring',
                    'tax', 'fee', 'surcharge', 'recovery', 'regulatory', 'universal',
                    'assessment', 'fund', 'compliance', 'property', 'access', 'network'
                ]) or
                
                # Technical service identifiers
                re.search(r'\d{7,}-\d{7,}|\d{10,}|[a-z]{2,}\d{6,}', service_desc) or
                
                # Bandwidth/speed measurements
                re.search(r'\d+(?:\.\d+)?\s*[gm]?bps?|x\s*\d+[gm]?|static|gbps|mbps', service_desc)
            )
            
            # Additional quality checks - exclude marketing content
            marketing_exclusions = [
                r"offers?.*solutions?", r"deliver.*performance", r"superior.*reliability",
                r"cost-effective", r"future-proofed", r"cloud-enabled", r"transforming.*into",
                r"tailored to support", r"match the right", r"business.*needs",
                r"trusted.*advisor", r"representative", r"learn more", r"contact.*today",
                r"complete.*offering", r"full suite", r"latest.*technology",
                r"please.*consider", r"in addition", r"as well as", r"often valued",
                r"business customers", r"service offering contains", r"traditional telecom",
                r"phone selection", r"private data network", r"access products"
            ]
            
            # Structural/Label exclusions
            structural_exclusions = [
                r"^(Invoice|Account|Customer).*:?$", r"^(Service|Finance|Current).*Charges?:?$",
                r"^(Total|Amount|Due|Previous|Payment).*:?$", r"^(Taxes?|Description|Location).*:?$",
                r"including.*charges", r"resulting.*grand", r"plus.*taxes",
                r"surcharges\.", r"space provided", r"feel free", r"invoice online",
                r".*report$", r".*summary$", r".*total:?$", r"^and [A-Z]", r"charges.*taxes",
                r"^&\s", r"^taxes?\s*&\s*surcharges?$"
            ]
            
            # Exclude if it contains marketing language
            if any(re.search(pattern, service_desc, re.IGNORECASE) for pattern in marketing_exclusions):
                keep_item = False
            
            # Exclude structural elements
            if any(re.search(pattern, service_desc, re.IGNORECASE) for pattern in structural_exclusions):
                keep_item = False
            
            # Exclude overly long descriptive text (likely marketing)
            if len(service_desc) > 80 and not re.search(r'\d+\.\d{2}', service_desc):
                keep_item = False
            
            # Exclude sentences that end with common marketing phrases
            if re.search(r'(performance|reliability|solutions|needs|organization|today)\.?$', service_desc, re.IGNORECASE):
                keep_item = False
            
            # Exclude pure labels (ending with colon, single words that are field names)
            if re.search(r':$', service_desc) or service_desc.lower() in ['description', 'amount', 'date', 'total', 'location', 'account', 'invoice', 'charges', 'surcharge']:
                keep_item = False
            
            if keep_item:
                # Handle duplicates by prioritizing Service Category Report
                if desc_key in seen_services:
                    existing_idx = seen_services[desc_key]["index"]
                    existing_section = seen_services[desc_key]["section"]
                    
                    # Prioritize Service Category Report over Individual Products Report
                    if (service_section == "Service Category Report" and 
                        existing_section == "Individual Products Report"):
                        # Replace the existing entry
                        filtered_services[existing_idx] = service
                        seen_services[desc_key] = {
                            "index": existing_idx, 
                            "section": service_section, 
                            "amount": service_amount
                        }
                    elif (existing_section == "Service Category Report" and 
                          service_section == "Individual Products Report"):
                        # Keep the existing Service Category Report entry, skip this one
                        continue
                    else:
                        # Different sections or both same priority - add as separate entry
                        filtered_services.append(service)
                        seen_services[f"{desc_key}_{service_section}"] = {
                            "index": len(filtered_services) - 1,
                            "section": service_section,
                            "amount": service_amount
                        }
                else:
                    # First occurrence of this service
                    filtered_services.append(service)
                    seen_services[desc_key] = {
                        "index": len(filtered_services) - 1,
                        "section": service_section,
                        "amount": service_amount
                    }
    
    parsed_data["Services_detail"] = filtered_services

    return parsed_data

@app.route('/extract_and_validate_document', methods=['POST'])
def extract_and_validate_document():
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400
    
    if file:
        try:
            # Read PDF directly from memory
            pdf_document = fitz.open(stream=file.read(), filetype="pdf")
            
            # Parse the PDF
            parsed_data = parse_pdf_to_document_annotation(pdf_document)
            
            # Validate against schema
            try:
                validate(instance=dict(parsed_data), schema=DOCUMENT_ANNOTATION_SCHEMA)
                is_valid = True
                validation_errors = []
            except ValidationError as e:
                is_valid = False
                validation_errors = [str(e)]
            
            # Create response maintaining order
            response_data = OrderedDict([
                ("is_valid_against_schema", is_valid),
                ("parsed_data", parsed_data),
                ("validation_errors", validation_errors)
            ])
            
            pdf_document.close()
            
            # Use json.dumps to preserve OrderedDict order
            import json
            response_json = json.dumps(response_data, indent=2)
            
            return app.response_class(
                response=response_json,
                status=200,
                mimetype='application/json'
            )
            
        except Exception as e:
            return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)