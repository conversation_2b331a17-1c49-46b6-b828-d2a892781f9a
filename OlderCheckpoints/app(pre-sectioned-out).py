from flask import Flask, request, jsonify, Response
import fitz  # PyMuPDF
import pymupdf4llm # For Markdown conversion
import io
import os
import logging
import json
import time
import re
import traceback

# --- Library Imports & Availability Checks ---
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    from markdown_it import MarkdownIt
    MARKDOWN_IT_AVAILABLE = True
except ImportError:
    MARKDOWN_IT_AVAILABLE = False

app = Flask(__name__)

# --- Basic Logging Configuration ---
logging.basicConfig(level=logging.INFO)
app.logger.setLevel(logging.INFO) 

if not BS4_AVAILABLE:
    app.logger.warning("'beautifulsoup4' (bs4) not found. HTML processing capabilities will be limited.")
if not MARKDOWN_IT_AVAILABLE:
    app.logger.warning("'markdown-it-py' (markdown_it) not found. Markdown parsing will fall back to basic HTML stripping if possible, or be very limited.")

# --- Configuration for PDF processing ---
MIN_TEXT_THRESHOLD_DEFAULT = 20
DPI_FOR_OCR_DEFAULT = 300
OCR_LANGUAGE_DEFAULT = "eng"


# --- START: SANITATION HELPER FUNCTIONS ---

def normalize_text(text):
    if not text:
        return ""
    text = text.replace("'", "'").replace("'", "'").replace(""", '"').replace(""", '"')
    text = text.replace('\u00A0', ' ')
    text = re.sub(r'\s+', ' ', text).strip()
    return " ".join(text.lower().strip().split())

def get_text_from_html_using_bs(html_content):
    if not html_content:
        return ""
    if not BS4_AVAILABLE:
        app.logger.warning("get_text_from_html_using_bs called but BeautifulSoup is not available. Applying basic regex strip.")
        text = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.DOTALL | re.IGNORECASE)
        text = re.sub(r'<[^>]+>', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
        
    try:
        soup = BeautifulSoup(html_content, "lxml")
    except Exception as e_lxml:
        app.logger.warning(f"BeautifulSoup failed with lxml: {e_lxml}. Trying html.parser.")
        try:
            soup = BeautifulSoup(html_content, "html.parser")
        except Exception as e_parser:
            app.logger.error(f"BeautifulSoup parsing error with html.parser: {e_parser}. Returning raw content.")
            return html_content
            
    for script_or_style in soup(["script", "style"]):
        script_or_style.decompose()

    for table in soup.find_all("table"):
        table_text_rows = []
        for row in table.find_all("tr"):
            cells = [cell.get_text(separator=" ", strip=True) for cell in row.find_all(["th", "td"])]
            if any(c.strip() for c in cells):
                table_text_rows.append("\t".join(cells))
        table_content_str = "\n" + "\n".join(table_text_rows) + "\n"
        table.replace_with(soup.new_string(table_content_str))
        
    plain_text = soup.get_text(separator="\n", strip=True)
    return plain_text

def convert_markdown_to_plain_text_with_library(md_text):
    if not md_text:
        return ""
    html_from_markdown = md_text
    if MARKDOWN_IT_AVAILABLE:
        md_parser = MarkdownIt(options_update={"html": True})
        try:
            html_from_markdown = md_parser.render(md_text)
        except Exception as e:
            app.logger.warning(f"MarkdownIt render error: {e}. Will attempt to process input as direct HTML/text.")
    else:
        app.logger.warning("markdown-it-py not available. Input will be processed as direct HTML/text.")
    
    plain_text = get_text_from_html_using_bs(html_from_markdown)
    
    if plain_text:
        text = plain_text
        text = text.replace("\\$", "$")
        text = re.sub(r'\$\s*(.*?)\s*\$', r'\1', text)
        text = re.sub(r'\\quad', ' ', text); text = re.sub(r'\\square', '[ ]', text)
        text = re.sub(r'^\s*>\s?', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*[\*\-\+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'\n\s*\n+', '\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        plain_text = text.strip()
        
        # Apply OCR-specific corrections
        plain_text = correct_ocr_errors(plain_text)
        plain_text = clean_ocr_artifacts(plain_text)
        plain_text = reconstruct_broken_words(plain_text)
        
    return plain_text

def preprocess_pymupdf_text(text):
    if not text: return ""
    
    # Apply OCR-specific corrections first
    processed_text = correct_ocr_errors(text)
    processed_text = fix_concatenated_words(processed_text)  # New generalized fix
    processed_text = clean_ocr_artifacts(processed_text)
    processed_text = clean_generic_artifacts(processed_text)  # New generalized cleaning
    processed_text = reconstruct_broken_words(processed_text)
    processed_text = normalize_table_formatting(processed_text)  # New table normalization
    
    # Original preprocessing logic
    lines = processed_text.splitlines(); cleaned_lines = []
    for line in lines:
        temp_line = line.strip()
        temp_line = re.sub(r'^\s*(?:page|pg\.?)\s*\d+\s*(?:of\s*\d+)?\s*$', '', temp_line, flags=re.IGNORECASE)
        temp_line = re.sub(r'^\s*\d+\s*/\s*\d+\s*$', '', temp_line.strip())
        temp_line = re.sub(r'\s*(?:page|pg\.?)\s*\d+\s*(?:of\s*\d+)?\s*$', '', temp_line.strip(), flags=re.IGNORECASE)
        temp_line = re.sub(r'\s*\d+\s*/\s*\d+\s*$', '', temp_line.strip())
        if not temp_line: continue
        cleaned_lines.append(temp_line)
    processed_text = "\n".join(cleaned_lines)
    processed_text = re.sub(r'^\s*[-_=\*]{10,}\s*$', '', processed_text, flags=re.MULTILINE)
    
    # Apply structure-aware cleanup
    processed_text = structure_aware_cleanup(processed_text)
    
    return processed_text.strip()

def apply_block_removal_rules(text, rules_list):
    if not text or not rules_list:
        return text
    
    # First apply intelligent OCR-tolerant boilerplate removal
    processed_text = intelligent_boilerplate_removal(text)
    
    # Then apply specific rules
    for rule in rules_list:
        generated_pattern_str = ""
        rule_id = rule.get("id", "N/A")
        try:
            rule_type = rule.get("type", "starts_with")
            
            if rule_id == "gtt_important_message": 
                app.logger.info(f"--- DEBUG START (Rule '{rule_id}') ---")
                app.logger.info(f"Processing rule ID '{rule_id}'. Type: '{rule_type}'.")
                app.logger.info(f"Text BEFORE rule (first 700 chars): {repr(processed_text[:700])}")

            if rule_type == "between_exact_markers":
                start_marker = rule.get("start_marker"); end_marker = rule.get("end_marker")
                if start_marker and end_marker:
                    start_regex = re.escape(start_marker); end_regex = re.escape(end_marker)
                    generated_pattern_str = r'(?s)' + start_regex + r'.*?' + end_regex
                    processed_text = re.sub(generated_pattern_str, '', processed_text, flags=re.IGNORECASE)
            elif rule_type == "starts_with":
                starts_with_phrase = rule.get("starts_with")
                if not starts_with_phrase: continue
                start_phrase_regex = r"\s*" + re.escape(starts_with_phrase)
                if rule.get("ends_before_next_blank_line"):
                    generated_pattern_str = start_phrase_regex + r"[\s\S]*?(?=\n\s*\n|\Z)"
                elif rule.get("ends_before_phrase"):
                    ends_before_phrase_regex = re.escape(rule.get("ends_before_phrase"))
                    generated_pattern_str = start_phrase_regex + r"[\s\S]*?(?=" + ends_before_phrase_regex + r")"
                elif rule.get("ends_with_phrase"):
                    ends_with_phrase_regex = re.escape(rule.get("ends_with_phrase"))
                    generated_pattern_str = start_phrase_regex + r"[\s\S]*?" + ends_with_phrase_regex
                else:
                    generated_pattern_str = start_phrase_regex + r"[\s\S]*?(?=\n\s*\n|\Z)"
                
                if generated_pattern_str:
                    original_len = len(processed_text)
                    processed_text = re.sub(generated_pattern_str, '', processed_text, flags=re.DOTALL | re.IGNORECASE)
                    if rule_id == "gtt_important_message": 
                        app.logger.info(f"Applied pattern for '{rule_id}': {repr(generated_pattern_str)}")
                        if len(processed_text) < original_len: app.logger.info(f"Text was MODIFIED by rule '{rule_id}'.")
                        else: app.logger.info(f"Text was NOT MODIFIED by rule '{rule_id}' (pattern may not have matched as expected).")
                        app.logger.info(f"Text AFTER rule '{rule_id}' (first 700 chars): {repr(processed_text[:700])}")
                        app.logger.info(f"--- DEBUG END (Rule '{rule_id}') ---")
            elif rule_type == "regex_direct":
                generated_pattern_str = rule.get("pattern")
                if generated_pattern_str:
                    processed_text = re.sub(generated_pattern_str, '', processed_text, flags=re.DOTALL | re.IGNORECASE)
        except re.error as e:
            app.logger.warning(f"Regex error applying rule ID '{rule_id}': {e}. Pattern: {generated_pattern_str}")
        except Exception as ex:
            app.logger.warning(f"General error applying rule ID '{rule_id}': {ex}")
    return processed_text

boilerplate_removal_rules = [
    {   "id": "gtt_important_message", "type": "starts_with",
        "starts_with": "important messages gtt strives to continually", # You will likely need to adjust this based on logs
        "ends_before_next_blank_line": True },
    {   "id": "standard_terms_regex", "type": "regex_direct",
        "pattern": r"(?i)terms and conditions apply[\s\S]*?(please retain for your records|subject to change without notice)" },
    {   "id": "privacy_policy_regex", "type": "regex_direct",
        "pattern": r"(?i)privacy policy:[\s\S]*?our website for details" },
    {   "id": "fixed_call_language_block", "type": "between_exact_markers",
        "start_marker": "type of call language", "end_marker": "1-800-854-7784" }
]
# --- END: SANITATION HELPER FUNCTIONS ---

# --- START: OCR-SPECIFIC SANITIZATION FUNCTIONS ---

def correct_ocr_errors(text):
    """
    Corrects common OCR character recognition errors in invoice text.
    Focuses on preserving important data like invoice numbers, amounts, dates.
    """
    if not text:
        return ""
    
    # Common OCR character substitutions
    ocr_corrections = {
        # Numbers vs Letters
        r'\bO(?=\d)': '0',  # O followed by digit -> 0
        r'(?<=\d)O\b': '0',  # O preceded by digit -> 0
        r'\bl(?=\d)': '1',  # l followed by digit -> 1
        r'(?<=\d)l\b': '1',  # l preceded by digit -> 1
        r'\bI(?=\d)': '1',  # I followed by digit -> 1
        r'(?<=\d)I\b': '1',  # I preceded by digit -> 1
        r'\bS(?=\d)': '5',  # S followed by digit -> 5
        r'(?<=\d)S\b': '5',  # S preceded by digit -> 5
        r'\bG(?=\d)': '6',  # G followed by digit -> 6
        r'(?<=\d)G\b': '6',  # G preceded by digit -> 6
        
        # Currency and decimal corrections
        r'\$\s*O(\d)': r'$0\1',  # $O5.00 -> $05.00
        r'\$\s*l(\d)': r'$1\1',  # $l5.00 -> $15.00
        r'(\d)\s*O\s*(\d)': r'\1.0\2',  # 5 O 5 -> 5.05
        r'(\d)\s*o\s*(\d)': r'\1.0\2',  # 5 o 5 -> 5.05
        
        # Date corrections
        r'(\d{1,2})/O(\d)': r'\1/0\2',  # 5/O1/2025 -> 5/01/2025
        r'(\d{1,2})/l(\d)': r'\1/1\2',  # 5/l5/2025 -> 5/15/2025
        r'O(\d)/(\d)': r'0\1/\2',  # O5/01/2025 -> 05/01/2025
        r'l(\d)/(\d)': r'1\1/\2',  # l2/01/2025 -> 12/01/2025
        
        # Invoice number patterns
        r'(?i)invoice\s*#?\s*[il1]+(\d)': r'Invoice #1\1',  # Invoice lll234 -> Invoice #1234
        r'(?i)inv\s*[o0]*(\d)': r'INV\1',  # INVOO123 -> INV123
        
        # Common word corrections in invoice context
        r'(?i)\btota[il1]\b': 'Total',
        r'(?i)\bamoun[t1]\b': 'Amount',
        r'(?i)\bda[t1]e\b': 'Date',
        r'(?i)\bcus[t1]omer\b': 'Customer',
        r'(?i)\bven[d0]or\b': 'Vendor',
        r'(?i)\bse[r1]vice\b': 'Service',
        r'(?i)\bcha[r1]ge\b': 'Charge',
    }
    
    corrected_text = text
    for pattern, replacement in ocr_corrections.items():
        try:
            corrected_text = re.sub(pattern, replacement, corrected_text)
        except re.error as e:
            app.logger.warning(f"OCR correction regex error for pattern {pattern}: {e}")
    
    return corrected_text

def clean_ocr_artifacts(text):
    """
    Removes common OCR artifacts and noise that don't represent actual content.
    """
    if not text:
        return ""
    
    # Remove common OCR artifacts
    artifact_patterns = [
        r'[^\w\s\$\.\,\-\#\(\)\[\]\:\;\/\%\&\@\!\?]',  # Remove unusual characters
        r'(?m)^\s*[|Il1\[\]]{3,}\s*$',  # Lines of vertical bars/brackets
        r'(?m)^\s*[-_=\*\~]{5,}\s*$',  # Horizontal line artifacts
        r'(?m)^\s*[\.]{5,}\s*$',  # Dotted lines
        r'\s{3,}',  # Multiple spaces -> single space
        r'(?m)^\s*\d{1,2}\s*$',  # Standalone page numbers on their own lines
        r'(?m)^\s*[A-Z]{1,2}\s*$',  # Single/double letters on their own lines
    ]
    
    cleaned_text = text
    for pattern in artifact_patterns:
        try:
            cleaned_text = re.sub(pattern, ' ', cleaned_text)
        except re.error as e:
            app.logger.warning(f"Artifact removal regex error for pattern {pattern}: {e}")
    
    # Clean up excessive whitespace
    cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)
    cleaned_text = re.sub(r'[ \t]+', ' ', cleaned_text)
    
    return cleaned_text.strip()

def reconstruct_broken_words(text):
    """
    Fixes words that were broken across lines during OCR processing.
    Common in invoice line items and addresses.
    """
    if not text:
        return ""
    
    # Patterns for word reconstruction
    reconstruction_patterns = [
        # Broken currency amounts
        (r'\$\s*\n\s*(\d)', r'$\1'),
        # Broken invoice numbers
        (r'(?i)(invoice|inv)\s*#?\s*\n\s*(\d)', r'\1 #\2'),
        # Broken dates
        (r'(\d{1,2})\s*\n\s*/\s*(\d)', r'\1/\2'),
        # Broken words with common prefixes/suffixes
        (r'(?i)(account|service|customer|total|amount|charge|due)\s*\n\s*([a-z]+)', r'\1\2'),
        # Broken decimal numbers
        (r'(\d+)\s*\n\s*\.\s*(\d+)', r'\1.\2'),
        # Common broken compound words
        (r'(?i)(sub)\s*\n\s*(total|scription)', r'\1\2'),
        (r'(?i)(tele)\s*\n\s*(com|phone)', r'\1\2'),
    ]
    
    reconstructed_text = text
    for pattern, replacement in reconstruction_patterns:
        try:
            reconstructed_text = re.sub(pattern, replacement, reconstructed_text, flags=re.MULTILINE)
        except re.error as e:
            app.logger.warning(f"Word reconstruction regex error for pattern {pattern}: {e}")
    
    return reconstructed_text

def intelligent_boilerplate_removal(text, similarity_threshold=0.8):
    """
    Enhanced boilerplate removal that uses fuzzy matching to handle OCR variations
    of common boilerplate text patterns.
    NOTE: Marketing removal is now handled earlier in the pipeline.
    """
    if not text:
        return ""
    
    # Marketing removal is now handled earlier in the pipeline, so skip it here
    cleaned_text = text
    
    # Common boilerplate patterns with OCR-tolerant regex
    ocr_tolerant_patterns = [
        # Privacy and legal notices (fuzzy patterns)
        r'(?i)privacy\s+polic[yi]\s*:?[\s\S]*?(?=\n\s*\n|visit\s+our\s+website|\Z)',
        r'(?i)terms\s+and\s+conditions\s+apply[\s\S]*?(?=\n\s*\n|subject\s+to\s+change|\Z)',
        r'(?i)please\s+retain\s+for\s+your\s+records[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Contact information blocks
        r'(?i)customer\s+service[\s\S]*?(?=\n\s*\n|visit\s+us|\Z)',
        r'(?i)for\s+questions[\s\S]*?(?=\n\s*\n|call\s+us|\Z)',
        
        # Regulatory notices (common across telecom invoices)
        r'(?i)regulatory\s+recovery[\s\S]*?(?=\n\s*\n|fee\s+schedule|\Z)',
        r'(?i)federal\s+universal\s+service[\s\S]*?(?=\n\s*\n|fund|\Z)',
        r'(?i)important\s+messages?[\s\S]*?(?=\n\s*\n|continues?\s+to|\Z)',
        
        # Payment instructions (often repetitive)
        r'(?i)payment\s+due[\s\S]*?(?=\n\s*\n|make\s+checks|\Z)',
        r'(?i)remit\s+payment[\s\S]*?(?=\n\s*\n|po\s+box|\Z)',
        
        # Generic footers/headers
        r'(?i)page\s+\d+\s+of\s+\d+',
        r'(?i)continued\s+on\s+next\s+page',
        r'(?i)confidential\s+and\s+proprietary',
    ]
    
    for pattern in ocr_tolerant_patterns:
        try:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.MULTILINE | re.DOTALL)
        except re.error as e:
            app.logger.warning(f"Intelligent boilerplate removal regex error for pattern {pattern}: {e}")
    
    # Remove blocks that are likely OCR artifacts of boilerplate
    lines = cleaned_text.split('\n')
    filtered_lines = []
    
    for line in lines:
        line_stripped = line.strip()
        if len(line_stripped) < 3:  # Very short lines
            continue
        
        # Skip lines that are likely boilerplate artifacts
        if re.match(r'^[^\w]*$', line_stripped):  # Only punctuation/symbols
            continue
        if re.match(r'^\d+$', line_stripped):  # Only numbers (likely page numbers)
            continue
        if len(set(line_stripped)) <= 2:  # Very low character diversity
            continue
            
        filtered_lines.append(line)
    
    return '\n'.join(filtered_lines)

def structure_aware_cleanup(text):
    """
    Preserves important invoice structure while cleaning OCR artifacts.
    Identifies and preserves tables, line items, and key-value pairs.
    """
    if not text:
        return ""
    
    lines = text.split('\n')
    processed_lines = []
    
    for i, line in enumerate(lines):
        cleaned_line = line.strip()
        
        # Preserve lines that look like invoice data
        if any([
            re.search(r'\$[\d\.,]+', cleaned_line),  # Contains currency
            re.search(r'(?i)(invoice|account|customer|total|due|date|charge)', cleaned_line),  # Key terms
            re.search(r'\d{1,2}/\d{1,2}/\d{2,4}', cleaned_line),  # Dates
            re.search(r'#\d+', cleaned_line),  # Numbers with hash (likely invoice/account numbers)
            re.search(r'\b\d+\.\d+\b', cleaned_line),  # Decimal numbers
        ]):
            processed_lines.append(cleaned_line)
            continue
        
        # Preserve lines that look like table headers or structured data
        if re.search(r'(?i)(description|quantity|price|amount|period|service)', cleaned_line):
            processed_lines.append(cleaned_line)
            continue
        
        # Keep lines with reasonable content (not just artifacts)
        if len(cleaned_line) > 5 and re.search(r'[a-zA-Z]', cleaned_line):
            # Check if it's not just repetitive characters
            char_diversity = len(set(cleaned_line.lower())) / len(cleaned_line) if cleaned_line else 0
            if char_diversity > 0.3:  # Reasonable character diversity
                processed_lines.append(cleaned_line)
    
    return '\n'.join(processed_lines)

def identify_marketing_blocks(text, min_words=30, marketing_threshold=0.3):
    """
    Identifies text blocks that appear to be marketing/promotional content.
    Returns a list of (start_pos, end_pos, score) tuples for marketing blocks.
    """
    if not text:
        return []
    
    print(f"\n=== MARKETING DETECTION DEBUG ===")
    print(f"Input text length: {len(text)} characters")
    print(f"Marketing threshold: {marketing_threshold}")
    print(f"First 500 chars of text: {text[:500]}...")
    
    # Enhanced marketing phrase patterns (case insensitive)
    marketing_phrases = [
        # Service descriptions - UPDATED for actual content
        r'\b(?:provides?|offering|offers?)\s+(?:a\s+)?(?:full\s+)?(?:suite|range|variety|array)\s+of\b',
        r'\b(?:comprehensive|complete|full|array)\s+(?:range|suite|selection|of)\s+(?:ip\s+)?(?:voice\s+)?(?:solutions?|services?)\b',
        r'\blatest\s+(?:in\s+)?(?:technology|products?|services?)\b',
        r'\btraditional\s+(?:telecom|telecommunication)\s+services?\b',
        r'\bcan\s+be\s+tailored\b',  # NEW: matches "which can be tailored"
        r'\bsupport\s+the\s+(?:voice\s+)?communications?\s+needs\b',  # NEW: matches actual content
        r'\bvoice\s+communications?\s+needs\s+(?:of\s+)?(?:your\s+)?business\b',  # NEW
        
        # SPECIFIC PATTERNS for the exact content in user's example
        r'\bbcn\s+has\s+an\s+array\s+of\s+ip\s+voice\s+solutions\b',  # NEW: exact match
        r'\bwhich\s+can\s+be\s+tailored\b',  # NEW: exact match 
        r'\bhas\s+an\s+array\s+of\s+ip\s+voice\s+solutions\b',  # NEW: broader match
        r'\barray\s+of\s+ip\s+voice\s+solutions\b',  # NEW: even broader
        
        # Call to action phrases
        r'\bcall\s+(?:today|now|us)\s+to\s+(?:learn|find|discover)\b',
        r'\bcontact\s+us\s+(?:today|now|for)\b',
        r'\blearn\s+more\s+about\s+our\b',
        r'\bplease\s+consider\s+us\s+for\b',
        r'\bvisit\s+(?:us\s+at\s+)?(?:our\s+)?(?:website|site)\b',  # UPDATED
        r'\bfor\s+more\s+information\b',
        r'\bwe\s+invite\s+you\s+to\s+visit\b',  # NEW: matches actual content
        
        # Product/service promotion
        r'\bhosted\s+pbx\s+services?\b',
        r'\bip\s+(?:phone\s+|voice\s+)?(?:selection|solutions?)\b',  # UPDATED
        r'\bprivate\s+data\s+network\s+solutions?\b',
        r'\binternet\s+access\s+products?\b',
        r'\bcloud\s+services?\s+for\s+business\b',
        r'\bvoice,?\s+data,?\s+(?:and\s+)?wireless\b',  # UPDATED
        r'\bvoice\s+data\s+cloud\s+wireless\b',  # NEW: matches exact content structure
        
        # Business/customer focus
        r'\bbusiness\s+customers?\b',
        r'\bcorporate\s+(?:clients?|customers?)\b',
        r'\benterprise\s+(?:solutions?|services?)\b',
        
        # Technology emphasis
        r'\bcutting[_\s]edge\s+technology\b',
        r'\bstate[_\s]of[_\s]the[_\s]art\b',
        r'\binnovative\s+solutions?\b',
        r'\badvanced\s+(?:technology|services?|solutions?)\b',
        
        # Website/company promotion - NEW PATTERNS
        r'\bwelcome\s+to\s+\w+\s+telecom\b',
        r'\binvite\s+you\s+to\s+visit\s+(?:the\s+)?(?:new\s+)?(?:site|website)\b',
        r'\bwww\.\w+\.com\b',  # Generic website pattern
    ]
    
    # Compile patterns for efficiency
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in marketing_phrases]
    
    # Split text into paragraphs
    paragraphs = re.split(r'\n\s*\n', text)
    marketing_blocks = []
    current_pos = 0
    
    print(f"Found {len(paragraphs)} paragraphs to analyze")
    
    for i, paragraph in enumerate(paragraphs):
        paragraph = paragraph.strip()
        if not paragraph:
            current_pos += len(paragraph) + 2  # Account for newlines
            continue
            
        words = paragraph.split()
        word_count = len(words)
        
        # Skip short paragraphs
        if word_count < min_words:
            current_pos += len(paragraph) + 2
            continue
        
        print(f"\n--- Analyzing paragraph {i+1} ({word_count} words) ---")
        print(f"Paragraph: {paragraph[:200]}...")
        
        # Count marketing phrase matches
        marketing_matches = 0
        matched_phrases = []
        for j, pattern in enumerate(compiled_patterns):
            matches = pattern.findall(paragraph)
            if matches:
                marketing_matches += len(matches)
                matched_phrases.extend(matches)
                print(f"  ✓ Marketing pattern {j+1} matched: {matches}")
        
        print(f"  Total marketing matches: {marketing_matches}")
        
        # Check for absence of invoice-specific data
        has_currency = bool(re.search(r'\$[\d\.,]+', paragraph))
        has_dates = bool(re.search(r'\d{1,2}/\d{1,2}/\d{2,4}', paragraph))
        has_account_numbers = bool(re.search(r'(?:account|invoice|customer)\s*#?\s*\d+', paragraph, re.IGNORECASE))
        has_invoice_terms = bool(re.search(r'(?:total|amount|due|charge|balance|payment)', paragraph, re.IGNORECASE))
        
        print(f"  Invoice data checks - Currency: {has_currency}, Dates: {has_dates}, Account#: {has_account_numbers}, Terms: {has_invoice_terms}")
        
        # Calculate marketing score
        marketing_density = marketing_matches / word_count if word_count > 0 else 0
        
        # Penalty for having invoice data
        invoice_data_penalty = 0
        if has_currency or has_dates or has_account_numbers:
            invoice_data_penalty = 0.5
        elif has_invoice_terms:
            invoice_data_penalty = 0.2
        
        final_score = max(0, marketing_density - invoice_data_penalty)
        
        print(f"  Marketing density: {marketing_density:.3f}, Invoice penalty: {invoice_data_penalty:.3f}, Final score: {final_score:.3f}")
        
        # Mark as marketing if score exceeds threshold
        if final_score >= marketing_threshold:
            start_pos = current_pos
            end_pos = current_pos + len(paragraph)
            marketing_blocks.append((start_pos, end_pos, final_score))
            print(f"  🎯 MARKETING BLOCK IDENTIFIED! Score: {final_score:.3f}")
            print(f"     Block text: {paragraph}")
        else:
            print(f"  ❌ Not marketing (score {final_score:.3f} < threshold {marketing_threshold})")
        
        current_pos += len(paragraph) + 2
    
    print(f"\n=== MARKETING DETECTION SUMMARY ===")
    print(f"Total marketing blocks found: {len(marketing_blocks)}")
    for i, (start, end, score) in enumerate(marketing_blocks):
        block_text = text[start:end]
        print(f"Block {i+1}: Score {score:.3f}, Length {end-start}, Text: {block_text[:100]}...")
    print("=== END MARKETING DETECTION ===\n")
    
    return marketing_blocks

def remove_promotional_text(text, min_words=30, marketing_threshold=0.3, preserve_context=True):
    """
    Removes marketing/promotional text blocks while preserving invoice data.
    
    Args:
        text: Input text to clean
        min_words: Minimum words in a block to consider for removal
        marketing_threshold: Score threshold for marketing content (0.0-1.0)
        preserve_context: If True, preserve text blocks near invoice data
    """
    if not text:
        return ""
    
    marketing_blocks = identify_marketing_blocks(text, min_words, marketing_threshold)
    
    if not marketing_blocks:
        return text
    
    # If preserve_context is enabled, filter out blocks near important data
    if preserve_context:
        filtered_blocks = []
        for start_pos, end_pos, score in marketing_blocks:
            # Check surrounding context for important invoice data
            context_start = max(0, start_pos - 200)
            context_end = min(len(text), end_pos + 200)
            context_text = text[context_start:context_end]
            
            # Look for important data near this block
            has_nearby_currency = bool(re.search(r'\$[\d\.,]+', context_text))
            has_nearby_invoice_numbers = bool(re.search(r'(?:invoice|account)\s*#?\s*\d+', context_text, re.IGNORECASE))
            has_nearby_totals = bool(re.search(r'(?:total|amount\s+due|balance)', context_text, re.IGNORECASE))
            
            # Only remove if no important data nearby, or if marketing score is very high
            if score > 0.6 or not (has_nearby_currency or has_nearby_invoice_numbers or has_nearby_totals):
                filtered_blocks.append((start_pos, end_pos, score))
                app.logger.debug(f"Marketing block marked for removal (score: {score:.3f})")
            else:
                app.logger.debug(f"Marketing block preserved due to nearby invoice data (score: {score:.3f})")
        
        marketing_blocks = filtered_blocks
    
    # Remove blocks in reverse order to maintain position accuracy
    cleaned_text = text
    for start_pos, end_pos, score in reversed(marketing_blocks):
        cleaned_text = cleaned_text[:start_pos] + cleaned_text[end_pos:]
    
    # Clean up excessive whitespace left by removals
    cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)
    
    app.logger.info(f"Removed {len(marketing_blocks)} marketing blocks from text")
    return cleaned_text.strip()

def detect_service_description_blocks(text):
    """
    Specifically targets service description blocks like the BCN Telecom example.
    These often start with company name and describe services offered.
    """
    if not text:
        return ""
    
    # Patterns for service description blocks
    service_description_patterns = [
        # Company + service description pattern
        r'(?i)(?:\w+\s+)*(?:telecom|communications?|technologies?|solutions?)\s+(?:provides?|offers?|delivers?|specializes?\s+in)[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Generic service offering blocks  
        r'(?i)(?:our|we)\s+(?:provide|offer|deliver|specialize\s+in)[\s\S]*?(?:services?|solutions?|products?)[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Long promotional paragraphs (50+ words with marketing terms)
        r'(?i)(?=.*\b(?:provides?|offers?|specializes?|delivers?)\b)(?=.*\b(?:services?|solutions?|products?)\b)(?=.*\b(?:business|customers?|clients?)\b).{200,}?(?=\n\s*\n|\Z)',
        
        # Technology/product listing blocks
        r'(?i)(?:including|such\s+as|features?|contains?)[\s\S]*?(?:pbx|ip\s+phone|data\s+network|internet\s+access|cloud\s+services?)[\s\S]*?(?=\n\s*\n|\Z)',
    ]
    
    cleaned_text = text
    for pattern in service_description_patterns:
        try:
            matches = re.finditer(pattern, cleaned_text, re.MULTILINE | re.DOTALL)
            for match in reversed(list(matches)):  # Reverse to maintain positions
                matched_text = match.group()
                word_count = len(matched_text.split())
                
                # Only remove if it's a substantial block (30+ words) and doesn't contain invoice data
                if (word_count >= 30 and 
                    not re.search(r'\$[\d\.,]+', matched_text) and 
                    not re.search(r'\d{1,2}/\d{1,2}/\d{2,4}', matched_text) and
                    not re.search(r'(?:invoice|account|total|due|amount|payment)\s*#?\s*\d+', matched_text, re.IGNORECASE)):
                    
                    app.logger.debug(f"Removing service description block: {matched_text[:100]}...")
                    cleaned_text = cleaned_text[:match.start()] + cleaned_text[match.end():]
                    
        except re.error as e:
            app.logger.warning(f"Service description pattern error: {e}")
    
    return cleaned_text

def comprehensive_ocr_sanitization(text, apply_intelligent_boilerplate=True):
    """
    Comprehensive sanitization pipeline specifically designed for OCR output.
    Applies all OCR-specific cleaning functions in the optimal order.
    Enhanced with generalized false-positive reduction.
    """
    if not text:
        return ""
    
    print(f"\n🧹 === COMPREHENSIVE OCR SANITIZATION DEBUG ===")
    print(f"Starting comprehensive OCR sanitization with enhanced cleaning")
    print(f"Input length: {len(text)} characters")
    
    # Step 1: Basic OCR error correction (preserve marketing patterns)
    print("🔧 Step 1: Basic OCR error correction")
    sanitized_text = correct_ocr_errors(text)
    print(f"After OCR error correction: {len(sanitized_text)} chars")
    
    # Step 2: Enhanced marketing detection and removal (EARLY - before text gets modified)
    print("🎯 Step 2: Enhanced marketing detection (EARLY)")
    sanitized_text = enhance_marketing_detection(sanitized_text)
    print(f"After enhanced marketing detection: {len(sanitized_text)} chars")
    
    # Step 3: Remove promotional text (original function for additional coverage)
    print("🎯 Step 3: Remove promotional text")
    sanitized_text = remove_promotional_text(sanitized_text)
    print(f"After promotional text removal: {len(sanitized_text)} chars")
    
    # Step 4: Detect and remove service description blocks
    print("🎯 Step 4: Detect and remove service description blocks")
    sanitized_text = detect_service_description_blocks(sanitized_text)
    print(f"After service description block removal: {len(sanitized_text)} chars")
    
    # Step 5: Fix concatenated words (after marketing removal to avoid interference)
    print("🔧 Step 5: Fix concatenated words")
    sanitized_text = fix_concatenated_words(sanitized_text)
    print(f"After concatenated word fixes: {len(sanitized_text)} chars")
    
    # Step 6: Remove OCR artifacts and noise
    print("🔧 Step 6: Clean OCR artifacts")
    sanitized_text = clean_ocr_artifacts(sanitized_text)
    print(f"After OCR artifact removal: {len(sanitized_text)} chars")
    
    # Step 7: Clean generic artifacts (vendor-agnostic)
    print("🔧 Step 7: Clean generic artifacts")
    sanitized_text = clean_generic_artifacts(sanitized_text)
    print(f"After generic artifact removal: {len(sanitized_text)} chars")
    
    # Step 8: Reconstruct broken words
    print("🔧 Step 8: Reconstruct broken words")
    sanitized_text = reconstruct_broken_words(sanitized_text)
    print(f"After word reconstruction: {len(sanitized_text)} chars")
    
    # Step 9: Normalize table formatting
    print("🔧 Step 9: Normalize table formatting")
    sanitized_text = normalize_table_formatting(sanitized_text)
    print(f"After table normalization: {len(sanitized_text)} chars")
    
    # Step 10: Apply intelligent boilerplate removal (optional)
    if apply_intelligent_boilerplate:
        print("🔧 Step 10: Intelligent boilerplate removal")
        sanitized_text = intelligent_boilerplate_removal(sanitized_text)
        print(f"After intelligent boilerplate removal: {len(sanitized_text)} chars")
    
    # Step 11: Structure-aware cleanup
    print("🔧 Step 11: Structure-aware cleanup")
    sanitized_text = structure_aware_cleanup(sanitized_text)
    print(f"After structure-aware cleanup: {len(sanitized_text)} chars")
    
    # Step 12: Final normalization
    print("🔧 Step 12: Final normalization")
    sanitized_text = normalize_text(sanitized_text)
    print(f"Enhanced OCR sanitization complete. Final length: {len(sanitized_text)} chars")
    print("=== END COMPREHENSIVE SANITIZATION ===\n")
    
    return sanitized_text

# --- END: OCR-SPECIFIC SANITIZATION FUNCTIONS ---

# --- Your existing PDF processing functions ---
def make_serializable(obj):
    if isinstance(obj, dict): return {k: make_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list): return [make_serializable(elem) for elem in obj]
    elif isinstance(obj, fitz.Rect): return {"x0": obj.x0, "y0": obj.y0, "x1": obj.x1, "y1": obj.y1, "_type": "fitz.Rect"}
    elif isinstance(obj, fitz.Point): return {"x": obj.x, "y": obj.y, "_type": "fitz.Point"}
    elif isinstance(obj, fitz.Matrix): return list(obj)
    else: return obj

@app.route('/extract_text_original', methods=['POST'])
def extract_text_original():
    if 'file' not in request.files: return jsonify({'error': 'No file part'}), 400
    file = request.files['file'];
    if file.filename == '': return jsonify({'error': 'No selected file'}), 400
    if file:
        try:
            pdf_bytes = file.read(); doc = fitz.open(stream=pdf_bytes, filetype="pdf"); text = ""
            for page_num in range(len(doc)): text += doc.load_page(page_num).get_text()
            doc.close(); return jsonify({'text': text})
        except Exception as e: app.logger.error(f"Error in /extract_text_original: {e}"); return jsonify({'error': str(e)}), 500
    return jsonify({'error': 'File processing error in /extract_text_original'}), 500

@app.route('/extract_markdown_ocr', methods=['POST'])
def extract_markdown_ocr():
    if 'file' not in request.files: return jsonify({'error': 'No file part'}), 400
    file = request.files['file']
    if file.filename == '': return jsonify({'error': 'No selected file'}), 400
    original_doc = None; processed_doc = None
    try:
        pdf_bytes = file.read(); original_doc = fitz.open(stream=pdf_bytes, filetype="pdf"); processed_doc = fitz.open()
        min_text_threshold = request.form.get('min_text_threshold', MIN_TEXT_THRESHOLD_DEFAULT, type=int)
        dpi_for_ocr = request.form.get('dpi_for_ocr', DPI_FOR_OCR_DEFAULT, type=int)
        ocr_language = request.form.get('ocr_language', OCR_LANGUAGE_DEFAULT, type=str)
        app.logger.info(f"Processing PDF with {original_doc.page_count} pages. OCR: {min_text_threshold} chars, DPI: {dpi_for_ocr}, Lang: {ocr_language}")
        for page_num in range(original_doc.page_count):
            page = original_doc.load_page(page_num); existing_text = page.get_text("text")
            if len(existing_text.strip()) < min_text_threshold:
                app.logger.info(f"Page {page_num + 1}: Minimal text. OCRing."); pix = None; ocr_page_doc_temp = None
                try:
                    pix = page.get_pixmap(dpi=dpi_for_ocr)
                    if not pix.colorspace or pix.colorspace.name not in (fitz.csRGB.name, fitz.csGRAY.name): pix = fitz.Pixmap(fitz.csRGB, pix)
                    if pix.alpha: pix = fitz.Pixmap(pix, alpha=0)
                    ocr_layer_pdf_bytes = pix.pdfocr_tobytes(language=ocr_language)
                    if ocr_layer_pdf_bytes:
                        ocr_page_doc_temp = fitz.open("pdf", ocr_layer_pdf_bytes); processed_doc.insert_pdf(ocr_page_doc_temp)
                        app.logger.info(f"Page {page_num + 1}: OCR layer added.")
                    else:
                        app.logger.warning(f"Page {page_num + 1}: pdfocr_tobytes() empty. Copying original."); processed_doc.insert_pdf(original_doc, from_page=page.number, to_page=page.number)
                except Exception as ocr_error:
                    app.logger.error(f"Page {page_num + 1}: OCR Error: {ocr_error}. Copying original.")
                    if 'TESSDATA_PREFIX' in str(ocr_error) or 'tesseract' in str(ocr_error).lower(): app.logger.error("Tesseract hint: Check install & TESSDATA_PREFIX.")
                    processed_doc.insert_pdf(original_doc, from_page=page.number, to_page=page.number)
                finally:
                    if pix: pix = None
                    if ocr_page_doc_temp: ocr_page_doc_temp.close()
            else:
                app.logger.info(f"Page {page_num + 1}: Sufficient text. Copying original."); processed_doc.insert_pdf(original_doc, from_page=page.number, to_page=page.number)
        full_original_text_parts = [processed_doc.load_page(i).get_text("text") for i in range(processed_doc.page_count)]
        final_original_text = "\n\n".join(full_original_text_parts)
        markdown_chunks_raw = pymupdf4llm.to_markdown(doc=processed_doc, page_chunks=True)
        app.logger.info(f"Converted to {len(markdown_chunks_raw)} Markdown chunks.")
        return jsonify({"original_extracted_text": final_original_text, "llm_friendly_markdown_output": {"markdown_chunks": make_serializable(markdown_chunks_raw)}})
    except Exception as e:
        app.logger.error(f"Error in /extract_markdown_ocr: {e}", exc_info=True)
        if 'TESSDATA_PREFIX' in str(e) or 'tesseract' in str(e).lower(): return jsonify({'error': f"{e}. Tesseract hint."}), 500
        return jsonify({'error': str(e)}), 500
    finally:
        if original_doc: original_doc.close()
        if processed_doc: processed_doc.close()
    return jsonify({'error': 'File processing error in /extract_markdown_ocr'}), 500

# --- START: UPDATED SANITIZATION ENDPOINT ---
@app.route('/sanitize_texts', methods=['POST'])
def sanitize_texts_endpoint():
    """
    Enhanced sanitization endpoint specifically designed for OCR-extracted text.
    Handles both individual text input and dual-source data (Mistral + PyMuPDF).
    Applies comprehensive OCR error correction, marketing removal, and cleaning.
    """
    app.logger.info("Received request for /sanitize_texts")
    try:
        data_input = request.get_json()

        # Handle different input formats
        if isinstance(data_input, str):
            # Simple text string input
            return _process_single_text(data_input)
        elif isinstance(data_input, dict) and 'text' in data_input:
            # Single text object with options
            return _process_single_text(data_input.get('text', ''), data_input)
        elif isinstance(data_input, list) and len(data_input) >= 2:
            # Legacy dual-source format (Mistral + PyMuPDF)
            return _process_dual_source_data(data_input)
        else:
            return jsonify({"error": "Invalid data format. Expected text string, text object, or array with two objects (Mistral data, PyMuPDF data)"}), 400

    except Exception as e:
        app.logger.error(f"Error in /sanitize_texts endpoint: {str(e)}", exc_info=True)
        return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500

def _process_single_text(input_text, options=None):
    """Process a single text input with enhanced OCR sanitization."""
    if options is None:
        options = {}
    
    # Extract options
    apply_boilerplate_removal = options.get('apply_boilerplate_removal', True)
    remove_marketing = options.get('remove_marketing', True)
    marketing_threshold = options.get('marketing_threshold', 0.3)
    debug_mode = options.get('debug_marketing', False)  # Add debug option
    
    if not input_text:
        return jsonify({"original_text": "", "sanitized_text": "", "status": "empty_input"}), 200
    
    print(f"\n🔍 === PROCESSING SINGLE TEXT DEBUG ===")
    print(f"Input text length: {len(input_text)} characters")
    print(f"Marketing removal enabled: {remove_marketing}")
    print(f"Marketing threshold: {marketing_threshold}")
    print(f"Debug mode: {debug_mode}")
    print(f"First 1000 chars of input: {input_text[:1000]}...")
    print("=== START PROCESSING ===\n")
    
    # Count various issues before ANY cleanup for accurate metrics
    marketing_blocks_found = 0
    concatenated_words_found = 0
    table_artifacts_found = 0
    
    # CRITICAL: Count marketing blocks on completely original text FIRST
    if remove_marketing:
        print("🔍 Analyzing marketing blocks on ORIGINAL UNMODIFIED text...")
        marketing_blocks_found = len(identify_marketing_blocks(input_text, marketing_threshold=marketing_threshold))
        print(f"Marketing blocks found in original text: {marketing_blocks_found}")
        
        if debug_mode and marketing_blocks_found > 0:
            # Debug: log what marketing blocks were found
            blocks = identify_marketing_blocks(input_text, marketing_threshold=marketing_threshold)
            for i, (start, end, score) in enumerate(blocks):
                block_text = input_text[start:end]
                print(f"Marketing block {i+1} (score: {score:.3f}): {block_text[:100]}...")
    
    # Count concatenated words (common invoice terms without spaces)
    concatenated_patterns = [r'\baccountnumber\b', r'\bservicecharges\b', r'\bcustomermessages\b', 
                           r'\bamountstate\b', r'\bsurchargeaccount\b', r'\binvoicedate\b']
    for pattern in concatenated_patterns:
        concatenated_words_found += len(re.findall(pattern, input_text, re.IGNORECASE))
    
    # Count table artifacts (dash patterns, excessive spacing)
    table_artifact_patterns = [r'[-]{3,}[\s]*[-]{3,}', r'\s{3,}[A-Z]', r'^[^\w\s]*$']
    for pattern in table_artifact_patterns:
        table_artifacts_found += len(re.findall(pattern, input_text, re.MULTILINE))
    
    print(f"📊 Initial metrics - Marketing blocks: {marketing_blocks_found}, Concatenated words: {concatenated_words_found}, Table artifacts: {table_artifacts_found}")
    
    # Apply comprehensive OCR sanitization
    if remove_marketing and apply_boilerplate_removal:
        print("🧹 Applying comprehensive OCR sanitization...")
        sanitized_text = comprehensive_ocr_sanitization(input_text, apply_intelligent_boilerplate=True)
    else:
        print("🧹 Applying selective sanitization steps...")
        # Apply sanitization steps selectively with new enhancements
        sanitized_text = correct_ocr_errors(input_text)
        
        # Apply marketing removal EARLY if enabled
        if remove_marketing:
            print("🎯 Step 1: Enhanced marketing detection (EARLY)")
            sanitized_text = enhance_marketing_detection(sanitized_text)
            print("🎯 Step 2: Remove promotional text")
            sanitized_text = remove_promotional_text(sanitized_text, marketing_threshold=marketing_threshold)
            print("🎯 Step 3: Detect service description blocks")
            sanitized_text = detect_service_description_blocks(sanitized_text)
        
        # Apply other cleaning after marketing removal
        print("🔧 Step 4: Fix concatenated words")
        sanitized_text = fix_concatenated_words(sanitized_text)
        print("🔧 Step 5: Clean OCR artifacts")
        sanitized_text = clean_ocr_artifacts(sanitized_text)
        print("🔧 Step 6: Clean generic artifacts")
        sanitized_text = clean_generic_artifacts(sanitized_text)
        print("🔧 Step 7: Reconstruct broken words")
        sanitized_text = reconstruct_broken_words(sanitized_text)
        print("🔧 Step 8: Normalize table formatting")
        sanitized_text = normalize_table_formatting(sanitized_text)
        
        if apply_boilerplate_removal:
            print("🔧 Step 9: Apply boilerplate removal rules")
            sanitized_text = apply_block_removal_rules(sanitized_text, boilerplate_removal_rules)
        
        print("🔧 Step 10: Structure-aware cleanup")
        sanitized_text = structure_aware_cleanup(sanitized_text)
        print("🔧 Step 11: Final normalization")
        sanitized_text = normalize_text(sanitized_text)
    
    # Calculate improvement metrics
    original_lines = len(input_text.splitlines())
    sanitized_lines = len(sanitized_text.splitlines())
    compression_ratio = len(sanitized_text) / len(input_text) if input_text else 0
    
    print(f"\n✅ === PROCESSING COMPLETE ===")
    print(f"Original length: {len(input_text)} → Final length: {len(sanitized_text)}")
    print(f"Compression ratio: {compression_ratio:.3f}")
    print(f"Lines: {original_lines} → {sanitized_lines}")
    print("=== END PROCESSING DEBUG ===\n")
    
    response_data = {
        "original_text": input_text,
        "sanitized_text": sanitized_text,
        "metrics": {
            "original_length": len(input_text),
            "sanitized_length": len(sanitized_text),
            "original_lines": original_lines,
            "sanitized_lines": sanitized_lines,
            "compression_ratio": round(compression_ratio, 3),
            "lines_removed": original_lines - sanitized_lines,
            "marketing_blocks_found": marketing_blocks_found,
            "concatenated_words_found": concatenated_words_found,
            "table_artifacts_found": table_artifacts_found,
            "marketing_removal_enabled": remove_marketing,
            "marketing_threshold_used": marketing_threshold
        },
        "settings": {
            "boilerplate_removal": apply_boilerplate_removal,
            "marketing_removal": remove_marketing,
            "marketing_threshold": marketing_threshold,
            "enhanced_cleaning": True
        },
        "status": "enhanced_ocr_sanitization_complete"
    }
    
    # Add debug info if requested
    if debug_mode:
        response_data["debug"] = {
            "marketing_detection_threshold": marketing_threshold,
            "marketing_blocks_positions": identify_marketing_blocks(input_text, marketing_threshold=marketing_threshold)
        }
    
    return jsonify(response_data), 200

def _process_dual_source_data(data_input_array):
    """Process legacy dual-source format (Mistral + PyMuPDF data)."""
    if len(data_input_array) < 2:
        return jsonify({"error": "Invalid data format: Expected a JSON array containing two objects (Mistral data, PyMuPDF data)"}), 400

    mistral_data_obj = data_input_array[0] if (len(data_input_array) > 0 and isinstance(data_input_array[0], dict)) else {}
    pymupdf_data_obj = data_input_array[1] if (len(data_input_array) > 1 and isinstance(data_input_array[1], dict)) else {}

    # Extract Mistral text
    original_mistral_text_input = ""
    mistral_document_annotation = None
    if mistral_data_obj:
        pages = mistral_data_obj.get("pages")
        if pages and isinstance(pages, list):
            original_mistral_text_input = "\n\n".join(page.get("markdown", "") for page in pages if isinstance(page, dict))
        elif isinstance(mistral_data_obj.get("markdown"), str):
            original_mistral_text_input = mistral_data_obj.get("markdown", "")
        doc_annotation_raw = mistral_data_obj.get("document_annotation")
        if isinstance(doc_annotation_raw, str):
            try: mistral_document_annotation = json.loads(doc_annotation_raw)
            except json.JSONDecodeError:
                app.logger.warning("Mistral document_annotation (string) not valid JSON. Passing as raw string.")
                mistral_document_annotation = doc_annotation_raw
        elif isinstance(doc_annotation_raw, dict): mistral_document_annotation = doc_annotation_raw

    # Extract PyMuPDF text
    original_pymupdf_text_input = ""
    pymupdf_llm_markdown_output_obj = {}
    if pymupdf_data_obj:
        if isinstance(pymupdf_data_obj.get("original_extracted_text"), str):
            original_pymupdf_text_input = pymupdf_data_obj.get("original_extracted_text", "")
        elif isinstance(pymupdf_data_obj.get("llm_friendly_markdown_output"), dict):
            markdown_output_tmp = pymupdf_data_obj.get("llm_friendly_markdown_output", {})
            chunks = markdown_output_tmp.get("markdown_chunks")
            if chunks and isinstance(chunks, list):
                original_pymupdf_text_input = "\n\n".join(chunk.get("text", "") for chunk in chunks if isinstance(chunk, dict))
        if isinstance(pymupdf_data_obj.get("llm_friendly_markdown_output"), dict):
            pymupdf_llm_markdown_output_obj = pymupdf_data_obj.get("llm_friendly_markdown_output")

    # Apply comprehensive OCR sanitization to both texts
    app.logger.info(f"Processing dual-source data - Mistral: {len(original_mistral_text_input)} chars, PyMuPDF: {len(original_pymupdf_text_input)} chars")
    
    # Count marketing blocks for metrics
    mistral_marketing_blocks = len(identify_marketing_blocks(original_mistral_text_input)) if original_mistral_text_input else 0
    pymupdf_marketing_blocks = len(identify_marketing_blocks(original_pymupdf_text_input)) if original_pymupdf_text_input else 0
    
    # Sanitize both texts using comprehensive OCR sanitization
    sanitized_mistral_text = comprehensive_ocr_sanitization(original_mistral_text_input) if original_mistral_text_input else ""
    sanitized_pymupdf_text = comprehensive_ocr_sanitization(original_pymupdf_text_input) if original_pymupdf_text_input else ""
    
    app.logger.info(f"Sanitized dual-source data - Mistral: {len(sanitized_mistral_text)} chars, PyMuPDF: {len(sanitized_pymupdf_text)} chars")
    
    return jsonify({
        "original_mistral_text": original_mistral_text_input,
        "sanitized_mistral_text": sanitized_mistral_text,
        "mistral_document_annotation": mistral_document_annotation,
        "original_pymupdf_text": original_pymupdf_text_input,
        "sanitized_pymupdf_text": sanitized_pymupdf_text,
        "pymupdf_llm_markdown_output": pymupdf_llm_markdown_output_obj,
        "metrics": {
            "mistral_marketing_blocks_found": mistral_marketing_blocks,
            "pymupdf_marketing_blocks_found": pymupdf_marketing_blocks,
            "mistral_compression_ratio": round(len(sanitized_mistral_text) / len(original_mistral_text_input), 3) if original_mistral_text_input else 0,
            "pymupdf_compression_ratio": round(len(sanitized_pymupdf_text) / len(original_pymupdf_text_input), 3) if original_pymupdf_text_input else 0
        },
        "status": "dual_source_sanitization_complete"
    }), 200

# --- END: UPDATED SANITIZATION ENDPOINT ---

# --- START: DEBUG ENDPOINT FOR MARKETING DETECTION ---
@app.route('/debug_marketing', methods=['POST'])
def debug_marketing_endpoint():
    """
    Debug endpoint to test marketing detection without full sanitization pipeline.
    """
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Missing 'text' field in request body"}), 400
        
        input_text = data.get('text', '')
        marketing_threshold = data.get('marketing_threshold', 0.3)
        
        if not input_text:
            return jsonify({"marketing_blocks": [], "count": 0}), 200
        
        # Test marketing detection on raw text
        marketing_blocks = identify_marketing_blocks(input_text, marketing_threshold=marketing_threshold)
        
        # Format blocks for debugging
        debug_blocks = []
        for i, (start, end, score) in enumerate(marketing_blocks):
            block_text = input_text[start:end]
            debug_blocks.append({
                "block_number": i + 1,
                "start_position": start,
                "end_position": end,
                "score": round(score, 3),
                "text_preview": block_text[:200] + "..." if len(block_text) > 200 else block_text,
                "word_count": len(block_text.split())
            })
        
        return jsonify({
            "marketing_blocks_found": len(marketing_blocks),
            "threshold_used": marketing_threshold,
            "blocks": debug_blocks,
            "status": "debug_complete"
        }), 200
        
    except Exception as e:
        app.logger.error(f"Error in debug marketing endpoint: {str(e)}", exc_info=True)
        return jsonify({"error": f"Debug error: {str(e)}"}), 500
# --- END: DEBUG ENDPOINT FOR MARKETING DETECTION ---

@app.route('/test_marketing_detection', methods=['POST'])
def test_marketing_detection():
    """
    Test endpoint to debug marketing detection with known problematic text.
    """
    try:
        data = request.get_json()
        
        # Handle different input types
        if data is None:
            data = {}
        elif isinstance(data, list):
            # If it's a list, use the first element or empty dict
            data = data[0] if len(data) > 0 and isinstance(data[0], dict) else {}
        elif not isinstance(data, dict):
            data = {}
        
        # Use provided text or default test text from user's example
        test_text = data.get('text', 'voice data cloud wireless bcn telecom 5000 t-rex avenue, suite 375 boca raton, fl 33431 hotel bardo savannah accounts payable 700 drayton st savannah, ga 31401-5803 account information account number: invoice number: invoice date: invoice date: ******** ******** 5/1/2025 contact us phone: email: ************ visit us: www.bcntele.com invoice information invoice number: invoice date: due date: amount due: ******** 5/1/2025 5/1/2025 due upon receipt $442.92 billing summary---previous balance $442.92 payments - thank you! ($442.92) credits & adjustments $0.00 balance forward: $0.00 service charges: $366.90 usage: $0.00 finance charge: $0.00 taxes and surcharges: $76.02 total current charges: $442.92 total amount due: $442.92 bcn has an array of ip voice solutions which can be tailored')
        
        marketing_threshold = data.get('marketing_threshold', 0.1)  # Lower threshold for testing
        
        print(f"\n🧪 === TESTING MARKETING DETECTION ===")
        print(f"Data received: {type(data)} - {data}")
        print(f"Test text: {test_text[:200]}...")
        print(f"Using threshold: {marketing_threshold}")
        
        # Test basic marketing detection
        print("\n1. Testing identify_marketing_blocks:")
        marketing_blocks = identify_marketing_blocks(test_text, marketing_threshold=marketing_threshold)
        
        # Test enhanced marketing detection
        print("\n2. Testing enhance_marketing_detection:")
        enhanced_result = enhance_marketing_detection(test_text)
        
        # Test promotional text removal
        print("\n3. Testing remove_promotional_text:")
        promotional_result = remove_promotional_text(test_text, marketing_threshold=marketing_threshold)
        
        return jsonify({
            "original_text": test_text,
            "marketing_blocks_found": len(marketing_blocks),
            "marketing_blocks_details": [
                {
                    "start": start,
                    "end": end,
                    "score": score,
                    "text": test_text[start:end][:100] + "..." if len(test_text[start:end]) > 100 else test_text[start:end]
                }
                for start, end, score in marketing_blocks
            ],
            "enhanced_result_length": len(enhanced_result),
            "promotional_result_length": len(promotional_result),
            "text_reduced": len(test_text) > len(enhanced_result) or len(test_text) > len(promotional_result),
            "enhanced_result": enhanced_result,
            "promotional_result": promotional_result,
            "input_data_type": str(type(data)),
            "input_data": str(data)[:200] + "..." if len(str(data)) > 200 else str(data)
        }), 200
        
    except Exception as e:
        print(f"Error in test marketing detection: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "error": f"Test error: {str(e)}", 
            "error_type": str(type(e)),
            "traceback": traceback.format_exc()
        }), 500

@app.route('/test_marketing_simple', methods=['GET'])
def test_marketing_simple():
    """
    Simple test endpoint for marketing detection - no input required.
    """
    try:
        # Known problematic text from user's example
        test_text = 'voice data cloud wireless bcn telecom 5000 t-rex avenue, suite 375 boca raton, fl 33431 hotel bardo savannah accounts payable 700 drayton st savannah, ga 31401-5803 account information account number: invoice number: invoice date: invoice date: ******** ******** 5/1/2025 contact us phone: email: ************ visit us: www.bcntele.com invoice information invoice number: invoice date: due date: amount due: ******** 5/1/2025 5/1/2025 due upon receipt $442.92 billing summary---previous balance $442.92 payments - thank you! ($442.92) credits & adjustments $0.00 balance forward: $0.00 service charges: $366.90 usage: $0.00 finance charge: $0.00 taxes and surcharges: $76.02 total current charges: $442.92 total amount due: $442.92 bcn has an array of ip voice solutions which can be tailored'
        
        marketing_threshold = 0.1  # Lower threshold for testing
        
        print(f"\n🧪 === SIMPLE MARKETING TEST ===")
        print(f"Test text length: {len(test_text)}")
        print(f"Using threshold: {marketing_threshold}")
        
        # Test basic marketing detection
        print("\n1. Testing identify_marketing_blocks:")
        marketing_blocks = identify_marketing_blocks(test_text, marketing_threshold=marketing_threshold)
        
        # Test enhanced marketing detection
        print("\n2. Testing enhance_marketing_detection:")
        enhanced_result = enhance_marketing_detection(test_text)
        
        return jsonify({
            "test_type": "simple_marketing_detection",
            "original_length": len(test_text),
            "marketing_blocks_found": len(marketing_blocks),
            "enhanced_result_length": len(enhanced_result),
            "text_was_reduced": len(test_text) > len(enhanced_result),
            "reduction_amount": len(test_text) - len(enhanced_result),
            "original_text_preview": test_text[:200] + "...",
            "enhanced_result_preview": enhanced_result[:200] + "..." if enhanced_result else "EMPTY",
            "marketing_blocks": [
                {
                    "start": start,
                    "end": end, 
                    "score": score,
                    "text_preview": test_text[start:end][:100] + "..."
                }
                for start, end, score in marketing_blocks
            ]
        }), 200
        
    except Exception as e:
        print(f"Error in simple marketing test: {str(e)}")
        traceback.print_exc()
        return jsonify({
            "error": f"Simple test error: {str(e)}",
            "traceback": traceback.format_exc()
        }), 500

# --- Vendor Schema/Example Functionality (existing regular endpoints) ---
vendor_schemas_store = [
    {"Vendor Name": "GTT", "Invoice Number": "INV6333064", "Customer Number": "331552", "Current Charges": "311.71", "Finance Charges": "0.00", "Invoice Date": "5/22/22", "Current Charges Due (USD)": "311.71", "Services_detail": [{"CroCrown Jewel of Pacifica 12725 Center##Ct Dr S, Cerritos CA 90703 USA": {}, "APIvoice/********##3086580-1251286##CroCrown Jewel of Pa": {"1251286-3597667-##VPoint Basic PBX **********": "5/22/22, 6/21/22, MRC, 1, 21.95, 21.95", "1251286-3597673-##Subscriber Line Charge **********": "5/22/22, 6/21/22, MRC, 1, 7.50, 7.50", "1251286-3597681-##PICC **********": "5/22/22, 6/21/22, MRC, 1, 3.95, 3.95", "1251286-3597687-##Account Maintenance Fee **********": "5/22/22, 6/21/22, MRC, 1, 3.95, 3.95", "1251286-3597693-##Carrier Cost Recovery Fee": "5/22/22, 6/21/22, MRC, 1, 3.00, 3.00", "1251286-3597698-##Local Number Portability : **********": "5/22/22, 6/21/22, MRC, 1, 0.42, 0.42"}, "APIvoice/********##3086581-1251299##CroCrown Jewel of Pa": {"1251299-3597716-##VPoint Basic PBX **********": "5/22/22, 6/21/22, MRC, 1, 21.95, 21.95", "1251299-3597724-##Subscriber Line Charge **********": "5/22/22, 6/21/22, MRC, 1, 7.50, 7.50", "1251299-3597733-##PICC **********": "5/22/22, 6/21/22, MRC, 1, 3.95, 3.95", "1251299-3597741-##Carrier Cost Recovery Fee": "5/22/22, 6/21/22, MRC, 1, 3.00, 3.00", "1251299-3597748-##Local Number Portability : **********": "5/22/22, 6/21/22, MRC, 1, 0.42, 0.42"}, "APIvoice/********##3086582-1251303##CroCrown Jewel of Pa": {"1251303-3597775-##VPoint Basic PBX **********": "5/22/22, 6/21/22, MRC, 1, 21.95, 21.95", "1251303-3597782-##Subscriber Line Charge : **********": "5/22/22, 6/21/22, MRC, 1, 7.50, 7.50", "1251303-3597791-##PICC **********": "5/22/22, 6/21/22, MRC, 1, 3.95, 3.95", "1251303-3597797-##Carrier Cost Recovery Fee": "5/22/22, 6/21/22, MRC, 1, 3.00, 3.00", "1251303-3597805-##Local Number Portability : **********": "5/22/22, 6/21/22, MRC, 1, 0.42, 0.42"}, "APIvoice/01508392##3086583-1251311##CroCrown Jewel of Pa": {"1251311-3597828-##VPoint Basic PBX 5628095828": "5/22/22, 6/21/22, MRC, 1, 21.95, 21.95", "1251311-3597833-##Subscriber Line Charge 5628095828": "5/22/22, 6/21/22, MRC, 1, 7.50, 7.50", "1251311-3597841-##PICC 5628095828": "5/22/22, 6/21/22, MRC, 1, 3.95, 3.95", "1251311-3597849-##Carrier Cost Recovery Fee": "5/22/22, 6/21/22, MRC, 1, 3.00, 3.00", "1251311-3597856-##Local Number Portability : 5628095828": "5/22/22, 6/21/22, MRC, 1, 0.42, 0.42"}, "AP/voice/01508399##3086584-1251318##CroCrown Jewel of Pa": {"1251318-3597884-##VPoint Basic PBX 5628095907": "5/22/22, 6/21/22, MRC, 1, 21.95, 21.95", "1251318-3597891-##Subscriber Line Charge 5628095907": "5/22/22, 6/21/22, MRC, 1, 7.50, 7.50", "1251318-3597897-##PICC 5628095907": "5/22/22, 6/21/22, MRC, 1, 4.31, 4.31", "1251318-3597903-##Carrier Cost Recovery Fee": "5/22/22, 6/21/22, MRC, 1, 3.00, 3.00", "1251318-3597909-##Local Number Portability : 5628095907": "5/22/22, 6/21/22, MRC, 1, 0.42, 0.42"}, "AP/voice/01508408##3086585-1251327##CroCrown Jewel of Pa": {"1251327-3597936-##VPoint Basic PBX 5628096826": "5/22/22, 6/21/22, MRC, 1, 21.95, 21.95", "1251327-3597941-##Subscriber Line Charge 5628096826": "5/22/22, 6/21/22, MRC, 1, 7.50, 7.50", "1251327-3597947-##PICC 5628096826": "5/22/22, 6/21/22, MRC, 1, 4.31, 4.31", "1251327-3597953-##Carrier Cost Recovery Fee": "5/22/22, 6/21/22, MRC, 1, 3.00, 3.00", "1251327-3597958-##Local Number Portability : 5628096826": "5/22/22, 6/21/22, MRC, 1, 0.42, 0.42"}, "APIvoice/01508418##3086586-1251337##CroCrown Jewel of Pa": {"1251337-3597985-##VPoint Basic PBX 5628654901": "5/22/22, 6/21/22, MRC, 1, 21.95, 21.95", "1251337-3597992-##Subscriber Line Charge 5628654901": "5/22/22, 6/21/22, MRC, 1, 7.50, 7.50", "1251337-3597997-##PICC 5628654901": "5/22/22, 6/21/22, MRC, 1, 4.31, 4.31", "1251337-3598006-##Carrier Cost Recovery Fee": "5/22/22, 6/21/22, MRC, 1, 3.00, 3.00", "1251337-3598010-##Local Number Portability : 5628654901": "5/22/22, 6/21/22, MRC, 1, 0.42, 0.42"}, "Regulatory Recovery Surcharge##Surcharge##page3##entry1": {"Regulatory Recovery Surcharge##Surcharge": "0.08"}, "CA Teleconnect Fund (VoIP)##Tax##page3##entry1": {"CA Teleconnect Fund (VoIP)##Tax": "0.70"}, "Universal Lifeline Telephone Service Charge##Tax##page3##entry1": {"Universal Lifeline Telephone Service Charge##Tax": "4.41"}, "CASF (VoIP)##Tax##page3##entry1": {"CASF (VoIP)##Tax": "0.94"}, "CA High Cost Fund A (VoIP)##Tax##page3##entry1": {"CA High Cost Fund A (VoIP)##Tax": "0.63"}, "FUSF (VoIP)##Tax##page3##entry1": {"FUSF (VoIP)##Tax": "39.94"}, "TRS (VoIP)##Tax##page3##entry1": {"TRS (VoIP)##Tax": "1.05"}, "P.U.C. Fee (VoIP) NF##Tax##page3##entry1": {"P.U.C. Fee (VoIP) NF##Tax": "0.49"}, "FCC Regulatory Fee (VoIP)##Tax##page3##entry1": {"FCC Regulatory Fee (VoIP)##Tax": "0.70"}}]},
    {   "vendor_name": "Beta Solutions", "description": "Purchase order schema for Beta Solutions.",
        "schema": { "order_details": {"po_number": "PO-BETA-001"} }
    },
    {   "vendor_name": "BCN Telecom", "description": "Invoice schema for BCN Telecom.",
        "schema": { "invoice_details": {"invoice_number": "********", "invoice_date": "2025-05-01", "due_date": "2025-05-01", "amount_due": "442.92", "amount_enclosed": "442.92", "previous_balance": "442.92", "payments": "-442.92", "credits_and_adjustments": "0.00", "balance_forward": "0.00", "service_charges": "366.90", "usage": "0.00", "finance_charge": "0.00", "taxes_and_surcharges": "76.02", "total_current_charges": "442.92", "total_amount_due": "442.92", "account_number": "boc20326", "contact_us": {"phone": "************", "email": "<EMAIL>", "visit_us": "www.bcntele.com"}, "services_detail": [{"item_id_or_sku": "****************", "description": "1.25g x 35m cable internet", "quantity": "1", "unit_price": "330.95", "total_amount": "330.95", "service_period_start": "2025-05-01", "service_period_end": "2025-05-31", "charge_type": "recurring_charge"}, {"item_id_or_sku": "5 static ips", "description": "5 static ips", "quantity": "1", "unit_price": "20.00", "total_amount": "20.00", "service_period_start": "2025-05-01", "service_period_end": "2025-05-31", "charge_type": "recurring_charge"}] } }
    }
]

@app.route('/vendor_schemas', methods=['GET'])
def get_all_vendor_schemas():
    app.logger.info(f"Request received for all vendor schemas. Returning {len(vendor_schemas_store)} schemas.")
    return jsonify(vendor_schemas_store)

@app.route('/vendor_schemas/<string:vendor_name>', methods=['GET'])
def get_vendor_schema_by_name(vendor_name):
    app.logger.info(f"Request received for vendor schema: {vendor_name}")
    for vendor_schema_obj in vendor_schemas_store:
        if vendor_schema_obj.get("vendor_name", "").lower() == vendor_name.lower():
            return jsonify(vendor_schema_obj)
    app.logger.warning(f"Vendor schema not found for: {vendor_name}")
    return jsonify({"error": "Vendor schema not found", "vendor_name": vendor_name}), 404

# --- MODIFIED SSE Endpoint ---
@app.route('/vendor_schemas_sse')
def vendor_schemas_sse():
    def generate_schema_events():
        app.logger.info("SSE connection established for vendor schemas (finite stream - no heartbeat).")
        for schema_obj in vendor_schemas_store:
            try:
                event_data = json.dumps(schema_obj)
                # Using a more robust ID generation
                schema_id_part = schema_obj.get('vendor_name', 'unknown_vendor')
                unique_suffix = int(time.time() * 1000) # Milliseconds for more uniqueness
                schema_id = f"{schema_id_part}_{unique_suffix}"

                yield f"event: vendor_schema\nid: {schema_id}\ndata: {event_data}\n\n"
                app.logger.debug(f"Sent schema for {schema_obj.get('vendor_name', 'Unknown Vendor')} via SSE.")
            except TypeError as e:
                app.logger.error(f"Error serializing schema object to JSON for SSE: {schema_obj}. Error: {e}")
                error_event = json.dumps({"error": "Failed to serialize schema", "detail": str(e)})
                yield f"event: error\ndata: {error_event}\n\n"
        
        app.logger.info("All vendor schemas sent via SSE. Stream will now end (finite stream).")
        # NO HEARTBEAT LOOP HERE FOR THIS TEST

    return Response(generate_schema_events(), mimetype='text/event-stream')
# --- END MODIFIED SSE Endpoint ---

def fix_concatenated_words(text):
    """
    Fixes words that were incorrectly concatenated during OCR processing.
    Uses generic invoice terminology patterns that apply across vendors.
    """
    if not text:
        return ""
    
    # Generic invoice terminology concatenation patterns
    concatenation_fixes = {
        # Account/Customer information
        r'\baccountnumber\b': 'account number',
        r'\bcustomernumber\b': 'customer number',
        r'\binvoicenumber\b': 'invoice number',
        r'\binvoicedate\b': 'invoice date',
        r'\bduedate\b': 'due date',
        r'\bamountdue\b': 'amount due',
        r'\bamountenclosed\b': 'amount enclosed',
        r'\bpreviousbalance\b': 'previous balance',
        r'\bcurrentcharges\b': 'current charges',
        r'\btotalamount\b': 'total amount',
        
        # Service/Billing terms
        r'\bservicecharges\b': 'service charges',
        r'\bserviceperiod\b': 'service period',
        r'\bmonthlyrecurring\b': 'monthly recurring',
        r'\busagecharges\b': 'usage charges',
        r'\bfinancecharge\b': 'finance charge',
        r'\btaxesand\b': 'taxes and',
        r'\bsurcharges\b': 'surcharges',
        r'\brecurringcharge\b': 'recurring charge',
        r'\bonetimecharge\b': 'one time charge',
        
        # Payment/Contact terms
        r'\bpaymentdue\b': 'payment due',
        r'\bcontactus\b': 'contact us',
        r'\bcustomerservice\b': 'customer service',
        r'\bbillingsummary\b': 'billing summary',
        r'\bpaymentcoupon\b': 'payment coupon',
        r'\bremittance\b': 'remittance',
        
        # Common compound words that should be split
        r'\bamountstate\b': 'amount state',
        r'\bamountfederal\b': 'amount federal',
        r'\bamountlocal\b': 'amount local',
        r'\bamountrecurring\b': 'amount recurring',
        r'\bsurchargeaccount\b': 'surcharge account',
        r'\bsurchargenetwork\b': 'surcharge network',
        r'\bsurchargeproperty\b': 'surcharge property',
        r'\bsurchargeregulatory\b': 'surcharge regulatory',
        r'\bsurchargestate\b': 'surcharge state',
        r'\bsurchargefederal\b': 'surcharge federal',
        
        # Message/Description terms
        r'\bcustomermessages\b': 'customer messages',
        r'\bimportantmessages\b': 'important messages',
        r'\bservicedescription\b': 'service description',
        r'\blineitem\b': 'line item',
        r'\bunitprice\b': 'unit price',
        r'\btotalprice\b': 'total price',
    }
    
    fixed_text = text
    for pattern, replacement in concatenation_fixes.items():
        try:
            fixed_text = re.sub(pattern, replacement, fixed_text, flags=re.IGNORECASE)
        except re.error as e:
            app.logger.warning(f"Concatenation fix regex error for pattern {pattern}: {e}")
    
    return fixed_text

def normalize_table_formatting(text):
    """
    Normalizes table formatting inconsistencies that commonly occur across different invoices.
    Handles dash patterns, spacing, and table structure artifacts.
    """
    if not text:
        return ""
    
    normalized_text = text
    
    # Normalize table separator patterns
    table_normalization_patterns = [
        # Standardize dash separators (common in invoice tables)
        (r'[-]{3,}[\s]*[-]{3,}[\s]*[-]{3,}', '---'),  # Multiple dash groups -> single pattern
        (r'[-\s]{5,}', '---'),  # Long dash-space combinations -> clean dashes
        
        # Clean up excessive spacing in table headers
        (r'(?i)(description|quantity|price|amount|total|charge|service)\s{3,}', r'\1 '),
        (r'(?i)(account|invoice|customer|date|due|balance)\s{3,}', r'\1 '),
        
        # Normalize currency formatting in tables
        (r'\$\s+(\d)', r'$\1'),  # Remove space after dollar sign
        (r'(\d)\s+(\.\d{2})', r'\1\2'),  # Remove space before decimal
        
        # Clean up table totals formatting
        (r'(?i)total\s*:\s*\$?\s*(\d)', r'total: $\1'),
        (r'(?i)subtotal\s*:\s*\$?\s*(\d)', r'subtotal: $\1'),
        
        # Normalize percentage formatting
        (r'(\d)\s+%', r'\1%'),  # Remove space before percent
        
        # Clean up table column alignment artifacts
        (r'\s{2,}([A-Z]{2,})', r' \1'),  # Multiple spaces before uppercase words
        (r'([a-z])\s{2,}([A-Z])', r'\1 \2'),  # Multiple spaces between case changes
    ]
    
    for pattern, replacement in table_normalization_patterns:
        try:
            normalized_text = re.sub(pattern, replacement, normalized_text, flags=re.MULTILINE)
        except re.error as e:
            app.logger.warning(f"Table normalization regex error for pattern {pattern}: {e}")
    
    # Remove standalone table artifacts (common across invoices)
    lines = normalized_text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line_stripped = line.strip()
        
        # Skip lines that are likely table artifacts
        if re.match(r'^[-\s]{3,}$', line_stripped):  # Only dashes and spaces
            continue
        if re.match(r'^[|+\s]{3,}$', line_stripped):  # Table border characters
            continue
        if re.match(r'^[.]{3,}$', line_stripped):  # Dotted lines
            continue
        if re.match(r'^[=]{3,}$', line_stripped):  # Equal sign lines
            continue
        if line_stripped in ['---', '--- ---', '--- --- ---']:  # Common table separators
            continue
            
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def enhance_marketing_detection(text):
    """
    Enhanced marketing content detection with broader patterns that apply across vendors.
    Focuses on generic promotional language rather than vendor-specific content.
    """
    if not text:
        return ""
    
    print(f"\n=== ENHANCED MARKETING DETECTION DEBUG ===")
    print(f"Input text length: {len(text)} characters")
    
    # Generic marketing/promotional patterns (vendor-agnostic) - UPDATED
    enhanced_marketing_patterns = [
        # Payment processing instructions (common across vendors)
        r'(?i)please\s+note\s+that\s+our\s+(?:remittance\s+)?address\s+has\s+changed[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)if\s+you\s+use\s+our\s+payment\s+slip[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)make\s+checks\s+payable\s+to[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)please\s+be\s+sure\s+to\s+update[\s\S]*?payment\s+address[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Generic service promotion (any vendor) - UPDATED for actual content
        r'(?i)(?:we|our\s+company|\w+\s+(?:inc|corp|llc|telecom|communications?))\s+(?:provides?|offers?|delivers?)[\s\S]*?(?:solutions?|services?|products?)[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)for\s+(?:more\s+)?information\s+about\s+our[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)to\s+learn\s+more\s+about[\s\S]*?(?:complete\s+service\s+offering|solutions?|services?)[\s\S]*?(?=\n\s*\n|\Z)',
        
        # BCN Telecom specific patterns - NEW
        r'(?i)bcn\s+has\s+an\s+array\s+of\s+ip\s+voice\s+solutions[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)which\s+can\s+be\s+tailored[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)support\s+the\s+voice\s+communications?\s+needs[\s\S]*?(?:of\s+)?(?:your\s+)?business[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)voice\s+data\s+cloud\s+wireless[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)welcome\s+to\s+\w+\s+telecom[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)we\s+invite\s+you\s+to\s+visit\s+(?:the\s+)?(?:new\s+)?(?:site|website)[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Technology/product descriptions (generic)
        r'(?i)(?:transforming|converting)[\s\S]*?(?:cost[_\s]effective|future[_\s]proof|cloud[_\s]enabled)[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)(?:reliability|performance|technology)[\s\S]*?(?:superior|advanced|innovative|cutting[_\s]edge)[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Business focus statements (generic)
        r'(?i)(?:talk\s+with|contact)\s+your\s+(?:trusted\s+)?(?:it\s+advisor|representative|account\s+manager)[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)let\s+us\s+match\s+the\s+right\s+technology[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Website/contact promotion - UPDATED
        r'(?i)(?:please\s+)?visit\s+us\s+at\s+www\.[\w.]+\s+to\s+(?:view|pay|learn)[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)welcome\s+to\s+\w+[\s\S]*?(?:please\s+visit|www\.)[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)www\.bcntele\.com[\s\S]*?(?=\n\s*\n|\Z)',  # NEW: specific to BCN
        
        # Payment/billing system notices
        r'(?i)it\s+is\s+important\s+to\s+make\s+this\s+change[\s\S]*?(?:avoid\s+delays|posted\s+late)[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)thank\s+you\s+for\s+your\s+attention\s+to\s+this\s+matter[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Generic company evolution/branding
        r'(?i)continues?\s+brand\s+evolution[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)please\s+detach\s+and\s+return[\s\S]*?bottom\s+portion[\s\S]*?(?=\n\s*\n|\Z)',
        
        # Multi-line marketing blocks - NEW PATTERNS for fragmented content
        r'(?i)voice\s+data\s+cloud\s+wireless[\s\S]*?bcn\s+telecom[\s\S]*?boca\s+raton[\s\S]*?(?=\n\s*\n|\Z)',
        r'(?i)bcn\s+telecom[\s\S]*?5000\s+t-rex\s+avenue[\s\S]*?boca\s+raton[\s\S]*?(?=\n\s*\n|\Z)',
        
        # DIRECT TEXT MATCHES for problematic content - NEW
        r'(?i)bcn\s+has\s+an\s+array\s+of\s+ip\s+voice\s+solutions\s+which\s+can\s+be\s+tailored',
        r'(?i)array\s+of\s+ip\s+voice\s+solutions\s+which\s+can\s+be\s+tailored',
        r'(?i)has\s+an\s+array\s+of\s+ip\s+voice\s+solutions',
        r'(?i)voice\s+data\s+cloud\s+wireless\s+bcn\s+telecom',
    ]
    
    removed_blocks = []
    cleaned_text = text
    original_length = len(text)
    
    for i, pattern in enumerate(enhanced_marketing_patterns):
        try:
            matches = list(re.finditer(pattern, cleaned_text, re.MULTILINE | re.DOTALL))
            if matches:
                print(f"Enhanced pattern {i+1} found {len(matches)} matches")
                for match in reversed(matches):  # Reverse to maintain positions
                    matched_text = match.group()
                    word_count = len(matched_text.split())
                    print(f"  Match: {matched_text[:100]}... ({word_count} words)")
                    
                    # Only remove if it's a substantial block and doesn't contain critical invoice data
                    if (word_count >= 10 and 
                        not re.search(r'\$[\d\.,]+', matched_text) and 
                        not re.search(r'\d{1,2}/\d{1,2}/\d{2,4}', matched_text) and
                        not re.search(r'(?:invoice|account|total|due|amount|payment)\s*#?\s*\d+', matched_text, re.IGNORECASE)):
                        
                        print(f"  ✂️ REMOVING marketing block: {matched_text[:100]}...")
                        removed_blocks.append(matched_text[:100])
                        cleaned_text = cleaned_text[:match.start()] + cleaned_text[match.end():]
                    else:
                        print(f"  ⚠️ PRESERVING block (contains invoice data)")
                        
        except re.error as e:
            print(f"Enhanced marketing detection regex error for pattern {i+1}: {e}")
    
    final_length = len(cleaned_text)
    print(f"\n=== ENHANCED MARKETING SUMMARY ===")
    print(f"Original length: {original_length}, Final length: {final_length}")
    print(f"Removed {len(removed_blocks)} marketing blocks")
    for i, block in enumerate(removed_blocks):
        print(f"  Block {i+1}: {block}...")
    print("=== END ENHANCED MARKETING ===\n")
    
    return cleaned_text

def clean_generic_artifacts(text):
    """
    Removes common artifacts that appear across different invoice types and vendors.
    """
    if not text:
        return ""
    
    # Generic artifact patterns (not vendor-specific)
    generic_artifact_patterns = [
        # Page numbering artifacts
        r'(?i)page\s+(?:number\s*:?\s*)?\d+(?:\s+of\s+\d+)?(?:\s*$|\n)',
        
        # Image/file references
        r'!\[[\w\-\.]+\]\([\w\-\.]+\)',  # Markdown image references
        r'(?i)img[-_]\d+\.(?:jpeg|jpg|png|gif)',  # Image filenames
        
        # Barcode/encoding artifacts
        r'\b\d{15,}\b',  # Long numeric strings (likely barcodes)
        r'[A-Z0-9]{20,}',  # Long alphanumeric strings
        
        # OCR confidence/processing artifacts
        r'(?i)(?:ocr|confidence|processing)\s*:?\s*\d+%?',
        
        # Generic separator artifacts
        r'^[^\w\s]*$',  # Lines with only symbols/punctuation
        
        # Empty field indicators
        r'\$_+',  # Dollar sign with underscores (blank amount fields)
        r'\$\s*_+',  # Dollar sign with spaced underscores
        
        # Repeated punctuation artifacts
        r'\.{4,}',  # More than 3 dots in a row
        r',{3,}',   # Multiple commas
        r':{3,}',   # Multiple colons
    ]
    
    cleaned_text = text
    for pattern in generic_artifact_patterns:
        try:
            cleaned_text = re.sub(pattern, ' ', cleaned_text, flags=re.MULTILINE)
        except re.error as e:
            app.logger.warning(f"Generic artifact removal regex error for pattern {pattern}: {e}")
    
    # Clean up excessive whitespace
    cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)
    cleaned_text = re.sub(r'[ \t]+', ' ', cleaned_text)
    
    return cleaned_text.strip()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=True)