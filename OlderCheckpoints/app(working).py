from flask import Flask, request, jsonify
import fitz  # PyMuPDF
import pymupdf4llm # For Markdown conversion
import io
import os

app = Flask(__name__)

# Configuration
MIN_TEXT_THRESHOLD_DEFAULT = 20
DPI_FOR_OCR_DEFAULT = 300
OCR_LANGUAGE_DEFAULT = "eng"

def make_serializable(obj):
    """
    Recursively converts PyMuPDF geometric objects (Rect, Point, Matrix)
    in a data structure to JSON-serializable formats.
    """
    if isinstance(obj, dict):
        return {k: make_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [make_serializable(elem) for elem in obj]
    elif isinstance(obj, fitz.Rect):
        return {"x0": obj.x0, "y0": obj.y0, "x1": obj.x1, "y1": obj.y1, "_type": "fitz.Rect"}
    elif isinstance(obj, fitz.Point):
        return {"x": obj.x, "y": obj.y, "_type": "fitz.Point"}
    elif isinstance(obj, fitz.Matrix):
        return list(obj)
    else:
        return obj

@app.route('/extract_text_original', methods=['POST'])
def extract_text_original():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    if file:
        try:
            pdf_bytes = file.read()
            pdf_document = fitz.open(stream=pdf_bytes, filetype="pdf")
            text = ""
            for page_num in range(len(pdf_document)):
                page = pdf_document.load_page(page_num)
                text += page.get_text()
            pdf_document.close()
            return jsonify({'text': text})
        except Exception as e:
            app.logger.error(f"Error in /extract_text_original: {str(e)}")
            return jsonify({'error': str(e)}), 500
    return jsonify({'error': 'File processing error in /extract_text_original'}), 500

@app.route('/extract_markdown_ocr', methods=['POST'])
def extract_markdown_ocr():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    if file:
        original_doc = None
        processed_doc = None
        try:
            pdf_bytes = file.read()
            original_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
            # This document will be built with original pages or pages with OCR layers
            processed_doc = fitz.open()

            min_text_threshold = request.form.get('min_text_threshold', MIN_TEXT_THRESHOLD_DEFAULT, type=int)
            dpi_for_ocr = request.form.get('dpi_for_ocr', DPI_FOR_OCR_DEFAULT, type=int)
            ocr_language = request.form.get('ocr_language', OCR_LANGUAGE_DEFAULT, type=str)

            app.logger.info(f"Processing PDF with {original_doc.page_count} pages. OCR threshold: {min_text_threshold} chars, DPI: {dpi_for_ocr}, Lang: {ocr_language}")

            for page_num in range(original_doc.page_count):
                page = original_doc.load_page(page_num)
                existing_text = page.get_text("text")

                if len(existing_text.strip()) < min_text_threshold:
                    app.logger.info(f"Page {page_num + 1}: Minimal text detected. Applying OCR.")
                    pix = None
                    ocr_page_doc_temp = None
                    try:
                        pix = page.get_pixmap(dpi=dpi_for_ocr)
                        if not pix.colorspace or pix.colorspace.name not in (fitz.csRGB.name, fitz.csGRAY.name):
                            pix = fitz.Pixmap(fitz.csRGB, pix)
                        if pix.alpha:
                            pix = fitz.Pixmap(pix, alpha=0)
                        
                        ocr_layer_pdf_bytes = pix.pdfocr_tobytes(language=ocr_language) # [cite: 17, 27]
                        if ocr_layer_pdf_bytes:
                            ocr_page_doc_temp = fitz.open("pdf", ocr_layer_pdf_bytes) # [cite: 27]
                            processed_doc.insert_pdf(ocr_page_doc_temp) # For PyMuPDF4LLM
                            app.logger.info(f"Page {page_num + 1}: OCR layer added to processed_doc.")
                        else:
                            app.logger.warning(f"Page {page_num + 1}: pdfocr_tobytes() returned empty. Copying original page to processed_doc.")
                            processed_doc.insert_pdf(original_doc, from_page=page.number, to_page=page.number)
                    except Exception as ocr_error:
                        app.logger.error(f"Page {page_num + 1}: Error during OCR: {str(ocr_error)}. Copying original page to processed_doc.")
                        if 'TESSDATA_PREFIX' in str(ocr_error) or 'tesseract' in str(ocr_error).lower():
                             app.logger.error("Tesseract error hint: Ensure Tesseract is installed in the container and TESSDATA_PREFIX is correctly set if needed.")
                        processed_doc.insert_pdf(original_doc, from_page=page.number, to_page=page.number)
                    finally:
                        if pix: pix = None
                        if ocr_page_doc_temp: ocr_page_doc_temp.close()
                else: # Sufficient text, no OCR
                    app.logger.info(f"Page {page_num + 1}: Sufficient text found. Skipping OCR. Copying original page to processed_doc.")
                    processed_doc.insert_pdf(original_doc, from_page=page.number, to_page=page.number)

            # 1. Extract full plain text from the processed_doc (which includes OCR layers where applied)
            # This is the "original data grabbed from (PyMuPDF and potentially Tesseract)"
            full_original_text_parts = []
            for page_num_processed in range(processed_doc.page_count):
                page_processed = processed_doc.load_page(page_num_processed)
                full_original_text_parts.append(page_processed.get_text("text"))
            
            final_original_text = "\n\n".join(full_original_text_parts) # Using double newline as a page separator

            # 2. Convert the same processed_doc to LLM-friendly Markdown
            markdown_chunks_raw = pymupdf4llm.to_markdown(doc=processed_doc, page_chunks=True) # [cite: 33, 39, 110]
            app.logger.info(f"Successfully converted processed_doc to {len(markdown_chunks_raw)} Markdown chunks.")

            # Make the Markdown result JSON serializable
            serializable_markdown_chunks = make_serializable(markdown_chunks_raw)

            return jsonify({
                "original_extracted_text": final_original_text,
                "llm_friendly_markdown_output": {
                    "markdown_chunks": serializable_markdown_chunks
                 }
            })

        except Exception as e:
            app.logger.error(f"Error in /extract_markdown_ocr: {str(e)}")
            if 'TESSDATA_PREFIX' in str(e) or 'tesseract' in str(e).lower():
                 return jsonify({'error': f"{str(e)}. Ensure Tesseract is installed in the container and TESSDATA_PREFIX is correctly set if needed."}), 500
            return jsonify({'error': str(e)}), 500
        finally:
            if original_doc: original_doc.close()
            if processed_doc: processed_doc.close()

    return jsonify({'error': 'File processing error in /extract_markdown_ocr'}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)