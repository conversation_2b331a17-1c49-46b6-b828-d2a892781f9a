## Automated Rebuild Script (Recommended)
### Unix/Linux/macOS:
```bash
./rebuild_container.sh
```

### Windows:
```cmd
rebuild_container.bat
```

## Manual Commands (Original)
### Build Container
```bash
docker build -t pymupdf-api .
```

### Run Container 
```bash
docker run -d -p 5001:5000 --name PyMuPDF-container pymupdf-api
```

### Manual Cleanup (if needed)
```bash
# Stop and remove container
docker stop PyMuPDF-container
docker rm PyMuPDF-container

# Remove image
docker rmi pymupdf-api
```

Test API w/ Postman at http://localhost:5001

## N8N integration

try both
http://localhost:5001/extract_text

http://PyMuPDF-container:5000/extract_text

Send Body: On
Body Content Type: Form-Data Multipart
Specifiy Body: Using Form-Data Parameters 

Add param:
Name : file
value: N8N output [ myfile.pdf, {{$json.myFile.data}} ]