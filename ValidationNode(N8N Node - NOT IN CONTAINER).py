import json
import difflib
from nltk.metrics.distance import edit_distance # Ensure NLTK is available
import re

# --- Pure Validation Functions (no sanitization) ---

def calculate_key_information_score(text1, text2):
    """Calculate score based on presence of key invoice information using universal patterns."""
    if not text1 or not text2:
        return 0.0
    
    # Define universal key invoice elements (no vendor-specific values)
    key_elements = [
        r'account\s*(?:number|#)?\s*:?\s*[A-Z0-9]{4,}',       # Generic account number
        r'invoice\s*(?:number|#)?\s*:?\s*\d{6,}',             # Generic invoice number
        r'\$\s*\d{1,4}(?:,\d{3})*\.\d{2}',                    # Reasonable currency amounts
        r'\d{1,2}/\d{1,2}/\d{2,4}',                           # Date patterns
        r'due\s+(?:upon\s+receipt|date|\d+)',                 # Due information
        r'(?:cable|internet|voice|data|phone)\s*(?:service|access|line)', # Service types
        r'(?:static|dedicated)\s*ips?',                       # IP services
        r'(?:modem|equipment|router)\s*(?:rental|charge)?',   # Equipment
        r'service\s*charges?',                                # Service charges
        r'taxes?\s*(?:and\s*)?surcharges?',                  # Tax information
        r'previous\s+balance',                                # Previous balance
        r'(?:payments?|credits?)\s*(?:-|thank)',             # Payments/credits
        r'total\s+(?:amount\s+)?due',                         # Total due
        r'payment\s*(?:coupon|method|due)',                   # Payment info
        r'(?:finance|late)\s*(?:charge|fee)s?',              # Finance charges
        r'usage\s*(?:charges?)?',                             # Usage charges
        r'balance\s+forward',                                 # Balance forward
        r'(?:billing|invoice)\s+(?:date|period)',            # Billing info
        r'contact\s+(?:us|information)',                      # Contact section
        r'phone:\s*\d',                                       # Phone number
        r'email:\s*\w+@\w+',                                  # Email address
        r'visit\s+us:\s*www\.',                               # Website
        r'[A-Z]{2,4}\s*(?:telecom|communications?|tech)',    # Generic company types
        r'p\.?o\.?\s*box\s*\d+',                             # PO Box
        r'\d+\s+[A-Z][a-z]+\s+(?:st|ave|rd|blvd|dr)',       # Street address
        r'[A-Z]{2}\s+\d{5}',                                 # State ZIP
    ]
    
    # Count elements present in both texts
    elements_in_text1 = 0
    elements_in_text2 = 0
    
    for element_pattern in key_elements:
        try:
            if re.search(element_pattern, text1, re.IGNORECASE):
                elements_in_text1 += 1
            if re.search(element_pattern, text2, re.IGNORECASE):
                elements_in_text2 += 1
        except re.error:
            continue
    
    # Calculate score based on how many elements are present in both
    total_possible = len(key_elements)
    avg_elements = (elements_in_text1 + elements_in_text2) / 2
    score = min(avg_elements / total_possible, 1.0)
    
    return score

def calculate_structural_similarity(text1, text2):
    """Calculate structural similarity between two texts using universal patterns."""
    if not text1 or not text2:
        return 0.0
    
    # Define universal structural patterns (applicable to any business invoice)
    structural_patterns = [
        r'(?i)account\s+(?:information|details?)',           # Account section
        r'(?i)invoice\s+(?:information|details?)',           # Invoice section
        r'(?i)billing\s+summary',                            # Billing section
        r'(?i)(?:service|product)\s+(?:charges?|details?)',  # Service section
        r'(?i)taxes?\s+(?:and\s+)?surcharges?',             # Tax section
        r'(?i)(?:payment|remittance)\s+(?:coupon|section)',  # Payment section
        r'(?i)contact\s+(?:us|information)',                 # Contact section
        r'(?i)transaction\s+(?:report|history)',             # Transaction section
        r'(?i)total\s+(?:amount\s+)?due',                    # Total due
        r'(?i)previous\s+balance',                           # Previous balance
        r'(?i)current\s+charges?',                           # Current charges
        r'(?i)due\s+(?:date|upon\s+receipt)',               # Due date
        r'(?i)(?:finance|late)\s+(?:charge|fee)s?',         # Finance charges
        r'(?i)balance\s+forward',                            # Balance forward
        r'(?i)credit\s+(?:adjustment|balance)',              # Credits
        r'(?i)payment\s+(?:received|applied)',               # Payments
    ]
    
    # Count structural elements in each text
    structure1_count = 0
    structure2_count = 0
    common_structures = 0
    
    for pattern in structural_patterns:
        try:
            found_in_1 = bool(re.search(pattern, text1))
            found_in_2 = bool(re.search(pattern, text2))
            
            if found_in_1:
                structure1_count += 1
            if found_in_2:
                structure2_count += 1
            if found_in_1 and found_in_2:
                common_structures += 1
        except re.error:
            continue
    
    # Calculate similarity based on common structures
    if structure1_count == 0 and structure2_count == 0:
        return 1.0  # Both have no structure
    
    total_unique_structures = structure1_count + structure2_count - common_structures
    if total_unique_structures == 0:
        return 1.0
    
    similarity = (2 * common_structures) / total_unique_structures
    return min(similarity, 1.0)

def calculate_fake_data_penalty(text1, text2):
    """Calculate penalty for presence of fake data using enhanced detection patterns."""
    
    # Enhanced fake data patterns with more precision
    severe_fake_patterns = [
        # Obviously fake or placeholder data - high penalty
        r'*********',                                    # Common placeholder number
        r'BOC00000|BON00000',                           # Fake account numbers
        r'fake|test|sample|placeholder|example',         # Fake data keywords
        r'lorem\s+ipsum',                               # Lorem ipsum text
        
        # Inconsistent company names in same document - high penalty
        r'BON\s+(?:telecom|website|list)',             # Wrong company mentions (should be BCN)
        
        # Marketing fluff that shouldn't be in invoices - medium penalty
        r'ultimate\s+user-friendly\s+experience',       # Marketing language
        r'tremendous\s+growth\s+and\s+expansion',       # Marketing content
        r'powerful\s+ally\s+in\s+finding\s+simplicity', # Marketing phrases
        
        # Image placeholders - medium penalty
        r'img-\d+\.jpeg',                               # Image references
        r'!\[.*?\]\(.*?\)',                             # Markdown images
    ]
    
    # Moderate fake patterns - lower penalty
    moderate_fake_patterns = [
        # Unrealistic monetary amounts for typical business invoices
        r'\$[1-9]\d{5,}\.\d{2}',                        # Very high amounts like $100k+ (raised threshold)
        
        # Multiple different invoice numbers in same document
        r'invoice.*?*********',                          # Fake invoice numbers
        
        # Unrealistic dates
        r'(?:13|14|15)/\d{1,2}/\d{4}',                  # Invalid months
        r'\d{1,2}/(?:32|33|34|35)/\d{4}',               # Invalid days
    ]
    
    penalty1 = 0
    penalty2 = 0
    
    # Process severe fake patterns (higher penalty)
    for pattern in severe_fake_patterns:
        try:
            matches1 = len(re.findall(pattern, text1, re.IGNORECASE))
            matches2 = len(re.findall(pattern, text2, re.IGNORECASE))
            
            # Each severe fake data element adds significant penalty
            penalty1 += matches1 * 0.15  # 15% penalty per severe fake element
            penalty2 += matches2 * 0.15
        except re.error:
            continue
    
    # Process moderate fake patterns (lower penalty)
    for pattern in moderate_fake_patterns:
        try:
            matches1 = len(re.findall(pattern, text1, re.IGNORECASE))
            matches2 = len(re.findall(pattern, text2, re.IGNORECASE))
            
            # Each moderate fake data element adds smaller penalty
            penalty1 += matches1 * 0.05  # 5% penalty per moderate fake element
            penalty2 += matches2 * 0.05
        except re.error:
            continue
    
    # Cross-validation: Check for major inconsistencies within each text
    for text, penalty_accumulator in [(text1, penalty1), (text2, penalty2)]:
        # Check for multiple different invoice numbers (major inconsistency)
        invoice_numbers = re.findall(r'invoice.*?(\d{6,})', text, re.IGNORECASE)
        unique_invoices = set(invoice_numbers)
        if len(unique_invoices) > 2:  # Allow some duplication, but flag if too many different numbers
            penalty_accumulator += 0.1  # 10% penalty for major inconsistency
    
    # Return the maximum penalty (worst case)
    total_penalty = max(penalty1, penalty2)
    return min(total_penalty, 0.8)  # Cap at 80% penalty (was 100%)

def extract_key_data_points_simple(text):
    """Extract key invoice data points for focused comparison using enhanced patterns."""
    if not text:
        return set()
    
    # Pre-normalize text for better extraction
    normalized_text = text.lower()
    # Normalize whitespace and line breaks for consistent matching
    normalized_text = re.sub(r'\s+', ' ', normalized_text)
    # Remove line breaks that split words
    normalized_text = re.sub(r'(\w)\n(\w)', r'\1 \2', normalized_text)
    
    data_points = set()
    
    # Extract currency amounts (normalized) - enhanced with better filtering
    amounts = re.findall(r'\$\s*(\d{1,6}(?:,\d{3})*(?:\.\d{2})?)', normalized_text)
    for amount in amounts:
        # Normalize amount format and filter out unrealistic amounts
        clean_amount = amount.replace(',', '')
        try:
            # Include reasonable business amounts (under $100K for most invoices)
            amount_value = float(clean_amount)
            if 0.01 <= amount_value <= 100000:  # Reasonable range for business invoices
                normalized_amount = f"${clean_amount}"
                data_points.add(normalized_amount)
        except ValueError:
            continue
    
    # Extract account numbers and IDs (enhanced patterns)
    account_patterns = [
        r'\b([a-z]{2,5}\d{4,12})\b',      # Alpha-numeric account IDs (case insensitive)
        r'\baccount.*?(\d{6,10})\b',      # Account followed by number
        r'\binvoice.*?(\d{6,10})\b',      # Invoice followed by number
        r'\b(\d{8,12})\b',               # Long numeric IDs
        r'\b(boc\d+)\b',                 # Specific account format (case insensitive)
    ]
    
    for pattern in account_patterns:
        matches = re.findall(pattern, normalized_text)
        for match in matches:
            if isinstance(match, tuple):
                for group in match:
                    if group and len(group) >= 4 and group.replace(' ', '').isalnum():
                        clean_match = re.sub(r'\s+', '', group).upper()  # Remove all spaces and normalize
                        data_points.add(clean_match)
            else:
                if len(match) >= 4 and match.replace(' ', '').isalnum():
                    clean_match = re.sub(r'\s+', '', match).upper()  # Remove all spaces and normalize
                    data_points.add(clean_match)
    
    # Extract dates (enhanced with better normalization and more patterns)
    date_patterns = [
        r'(\d{1,2})/(\d{1,2})/(\d{2,4})',  # MM/DD/YYYY
        r'(\d{1,2})-(\d{1,2})-(\d{2,4})',  # MM-DD-YYYY
        r'(\d{2,4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
        r'(\d{2})/(\d{2})/(\d{2})',        # MM/DD/YY
        r'(\d{2})/(\d{2})/(\d{4})',        # MM/DD/YYYY (specific)
    ]
    
    for pattern in date_patterns:
        dates = re.findall(pattern, normalized_text)
        for date_parts in dates:
            # Normalize to MM/DD/YYYY format with validation
            if len(date_parts) == 3:
                month, day, year = date_parts
                
                # Handle 2-digit years
                if len(year) == 2:
                    year = "20" + year if int(year) < 50 else "19" + year
                
                # Basic validation
                try:
                    month_int = int(month)
                    day_int = int(day)
                    year_int = int(year)
                    
                    if 1 <= month_int <= 12 and 1 <= day_int <= 31 and 1900 <= year_int <= 2100:
                        # Normalize format consistently
                        normalized_date = f"{month}/{day}/{year}"
                        data_points.add(normalized_date)
                        
                        # Also add zero-padded version for consistency
                        padded_date = f"{month.zfill(2)}/{day.zfill(2)}/{year}"
                        data_points.add(padded_date)
                except ValueError:
                    continue
    
    # Extract service/product descriptions (enhanced patterns with better normalization)
    service_patterns = [
        r'\b(\d+(?:\.\d+)?g?\s*(?:x\s*\d+m?)?\s*cable\s*internet)\b',     # Cable internet services
        r'\b(\d+\s*static\s*ips?)\b',                                       # Static IPs
        r'\b(standard\s*(?:modem\s*)?equipment)\b',                        # Equipment
        r'\b(internet\s*access)\b',                                         # Internet access
        r'\b(voice\s*(?:communications?|solutions?|services?))\b',          # Voice services
        r'\b(cable\s*internet)\b',                                          # Cable internet
        r'\b(voice\s*lines?)\b',                                           # Voice lines
        r'\b(finance\s*charges?)\b',                                       # Finance charges
        r'\b(service\s*charges?)\b',                                       # Service charges
        r'\b(taxes\s*(?:and\s*)?surcharges?)\b',                          # Taxes and surcharges
        r'\b(management\s*system)\b',                                      # Management system
        r'\b(right\s*technology)\b',                                       # Right technology (specific)
        r'\b(traditional\s*telecom)\b',                                    # Traditional telecom
    ]
    
    for pattern in service_patterns:
        matches = re.findall(pattern, normalized_text)
        for match in matches:
            # Normalize service descriptions - remove extra whitespace
            normalized_service = re.sub(r'\s+', ' ', match.strip())
            if len(normalized_service) > 3:
                data_points.add(normalized_service)
    
    # Extract key invoice terms (enhanced with more specific terms)
    invoice_term_patterns = [
        r'\b(due\s+upon\s+receipt)\b',
        r'\b(payment)\b',
        r'\b(total)\b', 
        r'\b(balance)\b',
        r'\b(invoice)\b',
        r'\b(account)\b',
        r'\b(bcn\s*telecom)\b',
        r'\b(voice\s*solutions)\b',
        r'\b(internet\s*access)\b',
        r'\b(traditional\s*telecom)\b',
        r'\b(management\s*system)\b',
        r'\b(right\s*technology)\b',
    ]
    
    for pattern in invoice_term_patterns:
        matches = re.findall(pattern, normalized_text)
        for term in matches:
            # Normalize terms - remove extra whitespace
            normalized_term = re.sub(r'\s+', ' ', term.strip())
            data_points.add(normalized_term)
    
    return data_points

def get_word_level_discrepancies(text1_str, text2_str):
    """Enhanced comparison function for word-level discrepancies with better normalization."""
    
    # Enhanced normalization for better comparison
    def normalize_text(text):
        # Convert to lowercase
        text = text.lower()
        # Handle line breaks that split words (common in PyMuPDF)
        text = re.sub(r'(\w)\n(\w)', r'\1 \2', text)
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text)
        # Remove common artifacts that don't affect content meaning
        text = re.sub(r'[^\w\s$.,/:-]', ' ', text)  # Keep important punctuation
        # Normalize currency formatting
        text = re.sub(r'\$\s*(\d)', r'$\1', text)  # Remove spaces in currency
        # Normalize date formatting
        text = re.sub(r'(\d{1,2})\s*/\s*(\d{1,2})\s*/\s*(\d{2,4})', r'\1/\2/\3', text)
        # Normalize account numbers (remove spaces)
        text = re.sub(r'\bboc\s*(\d+)', r'boc\1', text)
        return text.strip()
    
    # Apply normalization
    text1_normalized = normalize_text(text1_str)
    text2_normalized = normalize_text(text2_str)
    
    # Split into words for comparison
    text1_words = text1_normalized.split()
    text2_words = text2_normalized.split()

    matcher = difflib.SequenceMatcher(None, text1_words, text2_words)
    discrepancies = []
    
    for tag, i1, i2, j1, j2 in matcher.get_opcodes():
        if tag != 'equal':
            # Get the actual words for context
            words1 = ' '.join(text1_words[i1:i2]) if i1 < i2 else ''
            words2 = ' '.join(text2_words[j1:j2]) if j1 < j2 else ''
            
            # Filter out trivial differences
        if tag == 'replace':
                if words1 and words2:
                    # Check if it's just spacing/formatting differences
                    clean1 = re.sub(r'\s+', '', words1)
                    clean2 = re.sub(r'\s+', '', words2)
                    # Also check for line break differences
                    clean1_no_breaks = re.sub(r'[\n\r]+', '', clean1)
                    clean2_no_breaks = re.sub(r'[\n\r]+', '', clean2)
                    if clean1_no_breaks != clean2_no_breaks:  # Only report if actually different content
                        discrepancies.append(f"Content differs - PyMuPDF: '{words1}' | Mistral: '{words2}'")
        elif tag == 'delete':
                if words1 and len(words1) > 3:  # Only report meaningful deletions
                    discrepancies.append(f"Only in PyMuPDF: '{words1}'")
        elif tag == 'insert':
                if words2 and len(words2) > 3:  # Only report meaningful additions
                    discrepancies.append(f"Only in Mistral: '{words2}'")
    
    return discrepancies
    
def validate_dual_source_texts(input_from_http_node):
    """Main validation function for dual-source text comparison - pure validation only."""
    results = {}
    
    try:
        if not isinstance(input_from_http_node, dict):
            raise ValueError("Input from HTTP Request node is not a valid JSON object (dictionary).")

        # --- Extract texts and passed-through data from Flask API output ---
        sanitized_mistral_text = input_from_http_node.get("sanitized_mistral_text")
        sanitized_pymupdf_text = input_from_http_node.get("sanitized_pymupdf_text")
        
        # These are the raw texts that were originally sent to the Flask app for sanitation
        original_mistral_raw_text = input_from_http_node.get("original_mistral_text")
        original_pymupdf_raw_text = input_from_http_node.get("original_pymupdf_text")
        
        # These are the additional fields passed through by the Flask app
        mistral_doc_annotation = input_from_http_node.get("mistral_document_annotation")
        pymupdf_llm_output = input_from_http_node.get("pymupdf_llm_markdown_output")
        flask_status = input_from_http_node.get("status")

        # --- Input Validation ---
        error_messages = []
        if not isinstance(sanitized_mistral_text, str):
            error_messages.append("Sanitized Mistral text not found or not a string in API response.")
        if not isinstance(sanitized_pymupdf_text, str):
            error_messages.append("Sanitized PyMuPDF text not found or not a string in API response.")
        if not isinstance(original_mistral_raw_text, str):
            error_messages.append("Original Mistral raw text not found or not a string in API response.")
        if not isinstance(original_pymupdf_raw_text, str):
            error_messages.append("Original PyMuPDF raw text not found or not a string in API response.")

        if error_messages:
            results = {"error": "; ".join(error_messages), "status": "validation_input_data_error_from_api_response"}
            results["received_api_response"] = input_from_http_node 
            return results

        # --- Perform Comparison on PRE-SANITIZED texts (no additional sanitization) ---
        mistral_to_compare = sanitized_mistral_text or ""
        pymupdf_to_compare = sanitized_pymupdf_text or ""
        
        # Calculate edit distance on pre-sanitized content (no additional normalization)
        distance = edit_distance(mistral_to_compare, pymupdf_to_compare) 

        if len(pymupdf_to_compare) == 0: 
            validation_score = 1.0 if len(mistral_to_compare) == 0 else 0.0
        else:
            cer = distance / len(pymupdf_to_compare)
            validation_score = 1.0 - cer
        validation_score = max(0.0, min(1.0, validation_score))
        
        # Extract key data points for additional validation (using simple extraction)
        mistral_data_points = extract_key_data_points_simple(mistral_to_compare)
        pymupdf_data_points = extract_key_data_points_simple(pymupdf_to_compare)
        
        # Calculate data point overlap
        common_data_points = mistral_data_points & pymupdf_data_points
        total_unique_points = mistral_data_points | pymupdf_data_points
        
        data_point_coverage = len(common_data_points) / len(total_unique_points) if total_unique_points else 1.0

        # Calculate enhanced validation score that combines multiple factors
        # 1. Content similarity (edit distance)
        content_similarity = validation_score
        
        # 2. Data point coverage
        data_coverage = data_point_coverage
        
        # 3. Key information presence (weighted)
        key_info_score = calculate_key_information_score(mistral_to_compare, pymupdf_to_compare)
        
        # 4. Structural similarity
        structure_score = calculate_structural_similarity(mistral_to_compare, pymupdf_to_compare)
        
        # 5. Fake data penalty
        fake_data_penalty = calculate_fake_data_penalty(mistral_to_compare, pymupdf_to_compare)
        
        # Combine scores with weights (enhanced for better accuracy)
        enhanced_validation_score = (
            content_similarity * 0.15 +    # Edit distance similarity (reduced weight)
            data_coverage * 0.35 +         # Data point overlap (increased importance)
            key_info_score * 0.40 +        # Key invoice information (highest importance)
            structure_score * 0.10         # Structural similarity (maintained)
        ) * (1.0 - fake_data_penalty * 0.3)  # Reduced fake data penalty impact
        
        enhanced_validation_score = max(0.0, min(1.0, enhanced_validation_score))

        # --- Populate Results ---
        results["enhanced_validation_score"] = round(enhanced_validation_score, 4)
        results["enhanced_validation_percentage"] = round(enhanced_validation_score * 100, 2)
        results["data_point_coverage_score"] = round(data_point_coverage, 4)
        results["data_point_coverage_percentage"] = round(data_point_coverage * 100, 2)
        results["key_information_score"] = round(key_info_score, 4)
        results["structural_similarity_score"] = round(structure_score, 4)
        results["fake_data_penalty"] = round(fake_data_penalty, 4)
        results["status"] = f"comparison_complete (API_status: {flask_status or 'N/A'})"
        results["comparison_edit_distance"] = distance
        results["normalized_comparison"] = True  # Indicator that enhanced normalization was used

        # Word-level discrepancies on pre-sanitized text
        word_discrepancies_content_focused = get_word_level_discrepancies(pymupdf_to_compare, mistral_to_compare)
        results["word_level_discrepancies_content"] = word_discrepancies_content_focused
        
        results["compared_mistral_plain_text"] = mistral_to_compare # Pre-sanitized text
        results["compared_pymupdf_plain_text"] = pymupdf_to_compare   # Pre-sanitized text
        
        # Data point analysis
        results["mistral_data_points"] = list(mistral_data_points)
        results["pymupdf_data_points"] = list(pymupdf_data_points)
        results["common_data_points"] = list(common_data_points)
        results["missing_in_mistral"] = list(pymupdf_data_points - mistral_data_points)
        results["missing_in_pymupdf"] = list(mistral_data_points - pymupdf_data_points)

        # --- Populate with original raw texts and newly passed-through structured data ---
        results["original_mistral_input_text"] = original_mistral_raw_text
        results["original_pymupdf_input_text"] = original_pymupdf_raw_text
        
        results["passed_through_mistral_annotation"] = mistral_doc_annotation
        results["passed_through_pymupdf_llm_output"] = pymupdf_llm_output

        return results

    except Exception as e:
        import traceback
        results = {
            "error": f"Error processing input: {str(e)}",
            "traceback": traceback.format_exc(),
            "status": "error_processing_input"
        }
        return results

# --- N8N Python Code Node Execution ---
# N8N expects the code to return a list of output items directly

# Initialize the output list
result_items = []

# N8N provides 'items' as the input data
if not items:
    result_items.append({
        "json": {
            "error": "No input items received. Expected output from HTTP Request (Flask API call).", 
            "status": "error_no_input"
        }
    })
else:
    # Process the first item (assuming single item from HTTP Request)
    try:
        # Extract JSON data from the first input item
        if hasattr(items[0], 'json'):
            input_data = items[0].json
        else:
            input_data = items[0]
        
        # Convert proxy object to dictionary if needed
        if hasattr(input_data, 'to_py'):
            input_from_http_node = input_data.to_py()
        else:
            input_from_http_node = input_data
        
        # Run validation
        validation_results = validate_dual_source_texts(input_from_http_node)
        
        # Create output item
        result_items.append({
            "json": validation_results
        })
        
    except Exception as e:
        import traceback
        error_results = {
            "error": f"Error processing input: {str(e)}",
            "traceback": traceback.format_exc(),
            "status": "error_processing_input"
        }
        result_items.append({
            "json": error_results
        })

# Return the items for N8N (this is what N8N expects)
return result_items